#!/usr/bin/env python3
"""
更新Excel文件中的shares_per_listing字段和ADR股本调整

功能：
1. 在"总收入（亿美元）_yahoo"后、"总股本（亿）"前新增一列shares_per_listing
2. 复制原有股本列并添加_raw后缀，新增流通股本（亿）_wind列
3. 对不带后缀的列进行ADR调整（除以shares_per_listing）
4. 重新计算总股本（亿）和流通股本（亿）汇总列

使用方法:
python update_shares_per_listing_excel.py --target-dir his
python update_shares_per_listing_excel.py --target-dir his --max-workers 4
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import typer
from loguru import logger
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from tqdm import tqdm
import traceback
from concurrent.futures import ProcessPoolExecutor, as_completed
from multiprocessing import cpu_count

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, IbFundamentals, WindStock
from utils.excel_utils import auto_adjust_worksheet
from utils.mixin import NA_VALUES

app = typer.Typer()

# 动态生成日志文件名
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0,
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00",
    filter=__name__
)

def load_fundamentals_data() -> Tuple[Dict[int, float], Dict[int, any], Dict[int, float]]:
    """从数据库一次性加载基本面数据（shares_per_listing、拆股信息）"""
    logger.info("正在从数据库加载基本面数据...")
    
    shares_data = {}
    split_date_data = {}
    split_ratio_data = {}
    
    try:
        for record in IbFundamentals.select(IbFundamentals.conid, IbFundamentals.shares_per_listing, 
                                          IbFundamentals.most_recent_split_date, IbFundamentals.most_recent_split_ratio):
            conid = record.conid
            
            # shares_per_listing数据
            if record.shares_per_listing is not None:
                shares_data[conid] = record.shares_per_listing
                
            # 拆股日期数据
            if record.most_recent_split_date is not None:
                split_date_data[conid] = record.most_recent_split_date
                
            # 拆股比例数据
            if record.most_recent_split_ratio is not None:
                split_ratio_data[conid] = record.most_recent_split_ratio
        
        logger.info(f"已加载基本面数据: {len(shares_data)} 个shares_per_listing, {len(split_date_data)} 个拆股日期, {len(split_ratio_data)} 个拆股比例")
        return shares_data, split_date_data, split_ratio_data
        
    except Exception as e:
        logger.error(f"加载基本面数据时发生错误: {str(e)}")
        return {}, {}, {}

def load_wind_data() -> Tuple[Dict[str, float], Dict[str, float]]:
    """从数据库一次性加载Wind数据（share_issuing_mkt和ev3）"""
    logger.info("正在从数据库加载Wind数据...")
    
    wind_float_data = {}
    wind_market_cap_data = {}
    
    try:
        for record in WindStock.select(WindStock.isin_code, WindStock.share_issuing_mkt, WindStock.ev3):
            isin = record.isin_code
            
            # 流通股本数据
            if record.share_issuing_mkt is not None:
                wind_float_data[isin] = record.share_issuing_mkt / 1e8  # 转换为亿股单位
                
            # 市值数据
            if record.ev3 is not None:
                wind_market_cap_data[isin] = record.ev3 / 1e8  # 转换为亿美元单位
        
        logger.info(f"已加载Wind数据: {len(wind_float_data)} 个流通股本, {len(wind_market_cap_data)} 个市值")
        return wind_float_data, wind_market_cap_data
        
    except Exception as e:
        logger.error(f"加载Wind数据时发生错误: {str(e)}")
        return {}, {}

def adjust_for_adr(value: Any, shares_per_listing: Optional[float]) -> Any:
    """根据ADR转换比例调整股本数据"""
    if pd.isna(value) or shares_per_listing is None:
        return value
    if shares_per_listing == 0:
        logger.warning("shares_per_listing为0，无法进行除法运算")
        return value
    
    try:
        # 尝试转换为浮点数进行计算
        numeric_value = float(value)
        return numeric_value / shares_per_listing
    except (ValueError, TypeError):
        # 如果无法转换为数字，返回原值
        return value

def update_stable_id_sheet(df: pd.DataFrame, shares_data: Dict[int, float], split_date_data: Dict[int, any], split_ratio_data: Dict[int, float], wind_data: Dict[str, float], wind_market_cap_data: Dict[str, float]) -> pd.DataFrame:
    """更新稳定ID sheet的结构和数据"""
    logger.info("正在更新稳定ID sheet结构...")
    
    # 创建新的DataFrame副本
    df_new = df.copy()
    
    # 1. 添加shares_per_listing列
    logger.info("添加shares_per_listing列...")
    df_new['shares_per_listing'] = df_new['conid'].map(shares_data)
    
    # 2. 添加拆股信息列
    logger.info("添加拆股信息列...")
    df_new['most_recent_split_date'] = df_new['conid'].map(split_date_data)
    df_new['most_recent_split_ratio'] = df_new['conid'].map(split_ratio_data)
    
    # 3. 新增市值（亿美元）_wind列（从数据库获取）
    logger.info("新增市值（亿美元）_wind列...")
    df_new['市值（亿美元）_wind'] = df_new['isin'].map(wind_market_cap_data)
    
    # 4. 新增流通股本（亿）_wind列（从数据库获取）
    logger.info("新增流通股本（亿）_wind列...")
    df_new['流通股本（亿）_wind'] = df_new['isin'].map(wind_data)
    
    # 5. 初始化_raw列并复制原始数据（仅当需要ADR调整时）
    logger.info("初始化_raw列并复制原始数据（仅当需要ADR调整时）...")
    
    # 需要复制的列映射（总股本（亿）_yahoo不需要_raw因为不进行ADR调整）
    columns_to_copy = {
        '总股本（亿）_ib': '总股本（亿）_ib_raw',
        '总股本（亿）_wind': '总股本（亿）_wind_raw', 
        '流通股本（亿）_ib': '流通股本（亿）_ib_raw',
        '流通股本（亿）_yahoo': '流通股本（亿）_yahoo_raw'
    }
    
    # 先初始化所有_raw列为None
    for target_col in columns_to_copy.values():
        df_new[target_col] = None
    
    # 只有当shares_per_listing不为0且不为空时，才复制数据到_raw列
    for idx in df_new.index:
        shares_per_listing = df_new.at[idx, 'shares_per_listing']
        if pd.notna(shares_per_listing) and shares_per_listing != 0:
            # 需要ADR调整，复制原始数据到_raw列
            for source_col, target_col in columns_to_copy.items():
                if source_col in df_new.columns:
                    df_new.at[idx, target_col] = df_new.at[idx, source_col]
    
    logger.debug("_raw列数据复制完成")
    
    # 6. 对原有股本列进行ADR调整
    logger.info("对股本列进行ADR调整...")
    
    # 需要进行ADR调整的列（总股本（亿）_yahoo和流通股本（亿）_wind不进行ADR调整）
    columns_to_adjust = [
        '总股本（亿）_ib', '总股本（亿）_wind',
        '流通股本（亿）_ib', '流通股本（亿）_yahoo'
    ]
    
    for col in columns_to_adjust:
        if col in df_new.columns:
            logger.debug(f"调整列: {col}")
            for idx in df_new.index:
                shares_per_listing = df_new.at[idx, 'shares_per_listing']
                original_value = df_new.at[idx, col]
                
                # 只有当shares_per_listing不为0且不为空时，才进行ADR调整
                if pd.notna(shares_per_listing) and shares_per_listing != 0:
                    adjusted_value = adjust_for_adr(original_value, shares_per_listing)
                    df_new.at[idx, col] = adjusted_value
                # 如果不需要ADR调整，保持原始数据不变（不做任何操作）
    
    # 7. 重新计算汇总列
    logger.info("重新计算汇总列...")
    
    # 重新计算市值（亿美元）
    def calculate_market_cap(row):
        market_cap_ib = row.get('市值（亿美元）_ib')
        market_cap_yahoo = row.get('市值（亿美元）_yahoo')
        market_cap_wind = row.get('市值（亿美元）_wind')
        
        # 优先级：ib > yahoo > wind
        for value in [market_cap_ib, market_cap_yahoo, market_cap_wind]:
            if pd.notna(value) and value is not None:
                return value
        return None
    
    # 重新计算总股本（亿）
    def calculate_total_shares(row):
        shares_ib = row.get('总股本（亿）_ib')
        shares_wind = row.get('总股本（亿）_wind') 
        shares_yahoo = row.get('总股本（亿）_yahoo')
        
        # 注意：总股本（亿）_yahoo 不进行ADR调整，所以这里的yahoo值是原始值
        # 优先级：ib > wind > yahoo
        for value in [shares_ib, shares_wind, shares_yahoo]:
            if pd.notna(value) and value is not None:
                return value
        return None
    
    # 重新计算流通股本（亿）
    def calculate_float_shares(row):
        float_ib = row.get('流通股本（亿）_ib')
        float_yahoo = row.get('流通股本（亿）_yahoo')
        float_wind = row.get('流通股本（亿）_wind')
        
        # 优先级：ib > yahoo > wind  
        for value in [float_ib, float_yahoo, float_wind]:
            if pd.notna(value) and value is not None:
                return value
        return None
    
    df_new['市值（亿美元）'] = df_new.apply(calculate_market_cap, axis=1)
    df_new['总股本（亿）'] = df_new.apply(calculate_total_shares, axis=1)
    df_new['流通股本（亿）'] = df_new.apply(calculate_float_shares, axis=1)
    
    # 重新计算流通股本占总股本比例
    def calculate_float_to_total_ratio(row):
        total_shares = row.get('总股本（亿）')
        float_shares = row.get('流通股本（亿）')
        
        if total_shares is not None and total_shares != 0 and float_shares is not None:
            return (float_shares / total_shares) * 100
        return None
    
    df_new['流通股本占总股本比例（%）'] = df_new.apply(calculate_float_to_total_ratio, axis=1)
    
    logger.info("稳定ID sheet结构更新完成")
    return df_new

def reorder_columns(df: pd.DataFrame) -> pd.DataFrame:
    """重新排列列的顺序以符合新的结构"""
    logger.info("重新排列列顺序...")
    
    # 定义新的列顺序（基于stk_data_summary_Semaphore.py）
    new_column_order = [
        'ID', 'conid', 'conid_db', 'isin',  # 基础字段
        'symbol', 'TRADING', '剔除', '成交额>1500', '券池>100', '无相同实体', '类型白名单', 
        'stock_type', '中文名_futu', '中文名_wind', '名称_ib', 'local_symbol', 'assoc_entity_id',
        
        # 财务指标
        '市值（亿美元）', '市值（亿美元）_ib', '市值（亿美元）_yahoo', '市值（亿美元）_wind',
        '总收入（亿美元）', '总收入（亿美元）_ib', '总收入（亿美元）_yahoo',
        
        'shares_per_listing',  # ADR每份对应的普通股数量
        'most_recent_split_date', 'most_recent_split_ratio',  # 最近拆股信息
        '总股本（亿）', '总股本（亿）_ib', '总股本（亿）_wind', '总股本（亿）_yahoo',
        '总股本（亿）_ib_raw', '总股本（亿）_wind_raw',
        '流通股本（亿）', '流通股本（亿）_ib', '流通股本（亿）_yahoo', '流通股本（亿）_wind',
        '流通股本（亿）_ib_raw', '流通股本（亿）_yahoo_raw',
        
        '流通股本占总股本比例（%）',
    ]
    
    # 获取现有的所有列
    existing_columns = df.columns.tolist()
    
    # 构建最终的列顺序：先是新顺序中存在的列，然后是其他列
    final_columns = []
    
    # 添加新顺序中存在的列
    for col in new_column_order:
        if col in existing_columns:
            final_columns.append(col)
    
    # 添加其他未在新顺序中的列
    for col in existing_columns:
        if col not in final_columns:
            final_columns.append(col)
    
    # 重新排列DataFrame
    df_reordered = df[final_columns]
    
    logger.info(f"列顺序调整完成，共 {len(final_columns)} 列")
    return df_reordered

# 定义列配置：小数位数和列名列表（与stk_data_summary_Semaphore.py保持完全一致）
column_configs = {
    5: [  # 价格相关列
        '最高价_1M', '最低价_1M', '均价_1M', '收盘价_1M', 'ATR_1M',
        '最高价_3M', '最低价_3M', '均价_3M', 'ATR_3M',
        '最高价_6M', '最低价_6M', '均价_6M', 'ATR_6M',
        '最高价_1Y', '最低价_1Y', '均价_1Y', 'ATR_1Y',
        '最高价_2Y', '最低价_2Y', '均价_2Y', 'ATR_2Y',
        '最高价_3Y', '最低价_3Y', '均价_3Y', 'ATR_3Y',
        '目标均价_yahoo', '目标中位价_yahoo',
        '月均中间价_ib', '绝对盘口_ib', '月均中间价_yahoo', '绝对盘口_yahoo'
    ],
    4: [  # 亿相关列
        '总股本（亿）_ib_raw', '总股本（亿）_wind_raw',
        '总股本（亿）', '总股本（亿）_ib', '总股本（亿）_yahoo', '总股本（亿）_wind',
        '流通股本（亿）_ib_raw', '流通股本（亿）_yahoo_raw',
        '流通股本（亿）', '流通股本（亿）_ib', '流通股本（亿）_yahoo', '流通股本（亿）_wind',
        '市值（亿美元）', '市值（亿美元）_ib', '市值（亿美元）_yahoo', '市值（亿美元）_wind',
        '总收入（亿美元）', '总收入（亿美元）_ib', '总收入（亿美元）_yahoo',
        '毛利（亿美元）_yahoo', '总现金（亿美元）_yahoo', '总债务（亿美元）_yahoo',
        '月均现金利率-融券费率（%）', '月均融券费率（%）' , '现金利率-融券费率（%）', '融券费率（%）'
    ],
    3: [  # 比率和成交量相关列
        '市盈率', '市盈率_ib', '市盈率_yahoo', '市盈率_wind',
        '市净率', '市净率_ib', '市净率_yahoo', '市净率_wind',
        '换手率（%）_wind',
        '空头占总股本比例（%）_yahoo', '空头占流通股本比例（%）_yahoo',
        '日均成交量（万）_1M', '日均成交量（万）_3M', '日均成交量（万）_6M',
        '日均成交量（万）_1Y', '日均成交量（万）_2Y', '日均成交量（万）_3Y',
        '日均成交额（万美元）_1M', '日均成交额（万美元）_3M', '日均成交额（万美元）_6M',
        '日均成交额（万美元）_1Y', '日均成交额（万美元）_2Y', '日均成交额（万美元）_3Y'
    ],
    2: [  # 百分比相关列
        '股息收益率（%）', '股息收益率（%）_yahoo', '股息收益率（%）_wind',
        '空头比率（空头持仓/日成交量）_yahoo',
        '流通股本占总股本比例（%）',
        '盘口跳数_ib', '相对盘口（1/2000）_ib', '相对盘口（万一）_ib',
        '盘口跳数_yahoo', '相对盘口（1/2000）_yahoo', '相对盘口（万一）_yahoo'
    ]
}

def format_data(df: pd.DataFrame) -> pd.DataFrame:
    """对数据进行格式化，应用小数位数设置，与stk_data_summary_Semaphore.py保持一致"""
    logger.info("正在格式化数据...")
    
    # 统一处理小数位数格式化
    for decimal_places, columns in column_configs.items():
        for col in columns:
            if col in df.columns:
                df[col] = df[col].apply(lambda x: f"{x:.{decimal_places}f}" if pd.notnull(x) and not isinstance(x, str) else x)
    
    logger.info("数据格式化完成")
    return df

def format_all_sheets(excel_sheets: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """对所有sheet进行格式化，与stk_data_summary_Semaphore.py保持一致"""
    logger.info("正在格式化所有sheet...")
    
    # 统一处理小数位数格式化
    for decimal_places, columns in column_configs.items():
        for col in columns:
            for sheet_name, df in excel_sheets.items():
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"{x:.{decimal_places}f}" if pd.notnull(x) and not isinstance(x, str) else x)
    
    logger.info("所有sheet格式化完成")
    return excel_sheets

def update_single_file(file_info: Tuple[Path, Dict[int, float], Dict[int, any], Dict[int, float], Dict[str, float], Dict[str, float], bool, str]) -> Tuple[str, bool, Optional[str]]:
    """更新单个Excel文件，用于多进程处理"""
    file_path, shares_data, split_date_data, split_ratio_data, wind_data, wind_market_cap_data, backup, process_id = file_info
    try:
        result = update_excel_file(file_path, shares_data, split_date_data, split_ratio_data, wind_data, wind_market_cap_data, backup, process_id)
        return (file_path.name, result, None)
    except Exception as e:
        return (file_path.name, False, str(e))

def update_excel_file(file_path: Path, shares_data: Dict[int, float], split_date_data: Dict[int, any], split_ratio_data: Dict[int, float], wind_data: Dict[str, float], wind_market_cap_data: Dict[str, float], backup: bool = True, process_id: str = None) -> bool:
    """更新单个Excel文件"""
    try:
        logger.info(f"正在处理文件: {file_path}")
        
        # 备份原文件
        if backup:
            backup_path = file_path.with_suffix('.backup' + file_path.suffix)
            try:
                backup_path.write_bytes(file_path.read_bytes())
                logger.debug(f"已备份文件: {backup_path}")
            except Exception as e:
                logger.warning(f"备份文件失败: {e}")
        
        # 读取Excel文件
        excel_sheets = pd.read_excel(file_path, sheet_name=None, engine='openpyxl',
                                   na_values=NA_VALUES, keep_default_na=False)
        
        if '稳定ID' not in excel_sheets:
            logger.warning(f"文件 {file_path} 中未找到'稳定ID'工作表")
            return False
        
        # 更新稳定ID sheet
        df_stable_id = excel_sheets['稳定ID']
        logger.info(f"原始稳定ID sheet有 {len(df_stable_id)} 行，{len(df_stable_id.columns)} 列")
        
        # 执行更新
        df_updated = update_stable_id_sheet(df_stable_id, shares_data, split_date_data, split_ratio_data, wind_data, wind_market_cap_data)
        df_updated = reorder_columns(df_updated)
        
        # 不需要对most_recent_split_date进行.date()转换，通过Excel格式处理
        
        df_updated = format_data(df_updated)
        
        logger.info(f"更新后稳定ID sheet有 {len(df_updated)} 行，{len(df_updated.columns)} 列")
        
        # 更新excel_sheets字典
        excel_sheets['稳定ID'] = df_updated
        
        # 对所有sheet进行格式化
        excel_sheets = format_all_sheets(excel_sheets)
        
        # 保存更新后的Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            for sheet_name, df_sheet in excel_sheets.items():
                df_sheet.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # 应用Excel格式化
        apply_excel_formatting(file_path)
        
        logger.info(f"文件 {file_path} 更新完成")
        return True
        
    except Exception as e:
        logger.error(f"更新文件 {file_path} 时发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def apply_excel_formatting(file_path: Path):
    """应用Excel格式化，参考stk_data_summary_Semaphore.py的格式"""
    try:
        # 构建需要右对齐的列列表
        right_align_cols = []
        for cols in column_configs.values():
            right_align_cols.extend(cols)
        right_align_cols.extend(['分析师数量_yahoo', 'shares_per_listing'])
        
        # 加载工作簿并应用格式
        workbook = load_workbook(file_path)
        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
            
            # 创建自定义对齐方式配置
            column_alignments = {}
            for col_idx, column in enumerate(worksheet.columns, 1):
                header_value = column[0].value
                if header_value in right_align_cols:
                    column_alignments[get_column_letter(col_idx)] = 'right'
            
            # 应用自动调整和对齐
            auto_adjust_worksheet(worksheet, column_alignments=column_alignments)
            
            # 为日期列设置格式（四列时间列）- 放在auto_adjust_worksheet之后
            from datetime import datetime
            date_columns = ['上市时间_futu', '起始时间_db', '结束时间_db', 'most_recent_split_date']
            for col_idx, column in enumerate(worksheet.columns, 1):
                header_value = column[0].value
                if header_value in date_columns:
                    # 检查该列所有非空值的时间是否都是0点
                    non_null_datetimes = []
                    for row in range(2, worksheet.max_row + 1):  # 从第2行开始（跳过标题行）
                        cell = worksheet.cell(row=row, column=col_idx)
                        if cell.value is not None and isinstance(cell.value, datetime):
                            non_null_datetimes.append(cell.value)
                    
                    # 如果所有非空datetime值的时间都是0点，逐个单元格设置日期格式
                    if non_null_datetimes and all(
                        (t.hour == 0 and t.minute == 0 and t.second == 0) 
                        for t in non_null_datetimes
                    ):
                        for row in range(2, worksheet.max_row + 1):  # 从第2行开始（跳过标题行）
                            cell = worksheet.cell(row=row, column=col_idx)
                            if cell.value is not None:
                                cell.number_format = 'YYYY-MM-DD'
        
        workbook.save(file_path)
        logger.info(f"已应用Excel格式化: {file_path}")
        
    except Exception as e:
        logger.warning(f"应用Excel格式化时发生错误: {e}")
        # 格式化失败不影响数据更新的成功

@app.command()
def update(
    target_dir: str = typer.Option("his", "--target-dir", "-d", help="目标目录，默认为his"),
    pattern: str = typer.Option("*.xlsx", "--pattern", "-p", help="文件匹配模式，默认为*.xlsx"),
    backup: bool = typer.Option(True, "--backup/--no-backup", "-b", help="是否备份原文件"),
    max_workers: int = typer.Option(None, "--max-workers", "-w", help="最大并发进程数，默认为CPU核心数")
) -> None:
    """更新Excel文件中的shares_per_listing字段和ADR股本调整"""
    
    logger.info("开始更新Excel文件的shares_per_listing和ADR股本调整...")
    
    # 加载基本面数据（shares_per_listing和拆股信息）
    shares_data, split_date_data, split_ratio_data = load_fundamentals_data()
    if not shares_data:
        logger.error("未能加载到基本面数据，退出")
        raise typer.Exit(1)
    
    # 加载Wind数据（流通股本和市值）
    wind_data, wind_market_cap_data = load_wind_data()
    
    # 获取目标目录
    target_path = Path(target_dir)
    if not target_path.exists():
        logger.error(f"目标目录 {target_path} 不存在")
        raise typer.Exit(1)
    
    # 查找Excel文件
    excel_files = list(target_path.glob(pattern))
    if not excel_files:
        logger.error(f"在目录 {target_path} 中未找到匹配 {pattern} 的Excel文件")
        raise typer.Exit(1)
    
    logger.info(f"找到 {len(excel_files)} 个Excel文件需要处理")
    
    # 设置最大工作进程数
    if max_workers is None:
        max_workers = min(cpu_count(), len(excel_files))
    
    logger.info(f"使用 {max_workers} 个进程进行并行处理")
    
    # 准备文件信息数据
    file_infos = [(file_path, shares_data, split_date_data, split_ratio_data, wind_data, wind_market_cap_data, backup, f"proc_{i}") for i, file_path in enumerate(excel_files)]
    
    # 多进程处理文件
    success_count = 0
    failed_files = []
    
    if len(excel_files) == 1:
        # 单文件直接处理，避免多进程开销
        file_path = excel_files[0]
        if update_excel_file(file_path, shares_data, split_date_data, split_ratio_data, wind_data, wind_market_cap_data, backup, "single_proc"):
            success_count = 1
        else:
            failed_files.append(file_path.name)
    else:
        # 多文件使用多进程处理
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {executor.submit(update_single_file, file_info): file_info[0].name 
                             for file_info in file_infos}
            
            # 处理完成的任务
            for future in tqdm(as_completed(future_to_file), total=len(file_infos), desc="更新Excel文件"):
                file_name = future_to_file[future]
                try:
                    result_file_name, success, error_msg = future.result()
                    if success:
                        success_count += 1
                        logger.info(f"更新成功: {result_file_name}")
                    else:
                        failed_files.append(result_file_name)
                        if error_msg:
                            logger.error(f"更新失败: {result_file_name}, 错误: {error_msg}")
                        else:
                            logger.error(f"更新失败: {result_file_name}")
                except Exception as e:
                    failed_files.append(file_name)
                    logger.error(f"处理文件时发生异常: {file_name}, 错误: {str(e)}")
    
    # 输出结果
    logger.info(f"更新完成: {success_count}/{len(excel_files)} 个文件成功")
    
    if failed_files:
        logger.error(f"失败的文件: {', '.join(failed_files)}")
    else:
        logger.info("所有文件更新成功！")

@app.command()
def test(
    file_path: str = typer.Argument(..., help="要测试的Excel文件路径")
) -> None:
    """测试单个文件的更新"""
    
    logger.info(f"测试更新文件: {file_path}")
    
    # 加载基本面数据（shares_per_listing和拆股信息）
    shares_data, split_date_data, split_ratio_data = load_fundamentals_data()
    if not shares_data:
        logger.error("未能加载到基本面数据，退出")
        raise typer.Exit(1)
    
    # 加载Wind数据（流通股本和市值）
    wind_data, wind_market_cap_data = load_wind_data()
    
    # 更新文件
    file_path_obj = Path(file_path)
    if not file_path_obj.exists():
        logger.error(f"文件 {file_path} 不存在")
        raise typer.Exit(1)
    
    if update_excel_file(file_path_obj, shares_data, split_date_data, split_ratio_data, wind_data, wind_market_cap_data, backup=True, process_id="test"):
        logger.info("测试更新成功！")
    else:
        logger.error("测试更新失败！")
        raise typer.Exit(1)

if __name__ == "__main__":
    app()
