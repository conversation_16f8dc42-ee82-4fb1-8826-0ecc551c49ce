-- SQL脚本：删除vnpy_stk_us_ib_m_2206_250814bak中symbol带_的记录
-- 然后从vnpy_stk_us_ib_m_2206_250814复制temp_test_symbols中的数据

SET autocommit = 0;
SET foreign_key_checks = 0;

-- 步骤1：分批删除目标数据库中symbol带_的dbbardata记录（每批20万行）
-- 创建临时表存储需要删除的symbol
CREATE TEMPORARY TABLE temp_symbols_to_delete AS
SELECT DISTINCT symbol
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE symbol LIKE '%_%';

-- 分批删除dbbardata中的记录，每批20万行
DELETE_LOOP: LOOP
  SET @rows_deleted = (
    DELETE FROM vnpy_stk_us_ib_m_2206_250814bak.dbbardata
    WHERE symbol IN (SELECT symbol FROM temp_symbols_to_delete)
    LIMIT 200000
  );
  
  SELECT ROW_COUNT() as deleted_count;
  
  IF @rows_deleted < 200000 THEN
    LEAVE DELETE_LOOP;
  END IF;
END LOOP DELETE_LOOP;

-- 删除dbbaroverview中的记录（不需要分批，overview表通常较小）
DELETE FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE symbol IN (SELECT symbol FROM temp_symbols_to_delete);

-- 步骤2：分批复制temp_test_symbols中的数据（每批10个symbol）
-- 创建存储过程处理分批复制
DELIMITER //
CREATE PROCEDURE CopySymbolData()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE batch_symbol VARCHAR(50);
  DECLARE symbol_cursor CURSOR FOR
    SELECT DISTINCT symbol FROM temp_test_symbols;
  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
  
  DECLARE batch_count INT DEFAULT 0;
  DECLARE batch_symbols TEXT DEFAULT '';
  
  OPEN symbol_cursor;
  
  copy_loop: LOOP
    FETCH symbol_cursor INTO batch_symbol;
    
    IF done THEN
      -- 处理最后一批
      IF batch_count > 0 THEN
        SET @sql_query = CONCAT(
          'INSERT INTO vnpy_stk_us_ib_m_2206_250814bak.dbbardata ',
          '(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, ',
          'open_price, high_price, low_price, close_price) ',
          'SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, ',
          'open_price, high_price, low_price, close_price ',
          'FROM vnpy_stk_us_ib_m_2206_250814.dbbardata ',
          'WHERE symbol IN (', TRIM(TRAILING ',' FROM batch_symbols), ') ',
          'OR symbol LIKE CONCAT(REGEXP_REPLACE(symbol, "_.*", ""), "_%") ',
          'ON DUPLICATE KEY UPDATE ',
          'exchange = VALUES(exchange), datetime = VALUES(datetime), ',
          '`interval` = VALUES(`interval`), volume = VALUES(volume), ',
          'turnover = VALUES(turnover), open_interest = VALUES(open_interest), ',
          'open_price = VALUES(open_price), high_price = VALUES(high_price), ',
          'low_price = VALUES(low_price), close_price = VALUES(close_price)'
        );
        PREPARE stmt FROM @sql_query;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
      END IF;
      LEAVE copy_loop;
    END IF;
    
    SET batch_symbols = CONCAT(batch_symbols, '"', batch_symbol, '",');
    SET batch_count = batch_count + 1;
    
    -- 每10个symbol为一批
    IF batch_count = 10 THEN
      SET @sql_query = CONCAT(
        'INSERT INTO vnpy_stk_us_ib_m_2206_250814bak.dbbardata ',
        '(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, ',
        'open_price, high_price, low_price, close_price) ',
        'SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, ',
        'open_price, high_price, low_price, close_price ',
        'FROM vnpy_stk_us_ib_m_2206_250814.dbbardata ',
        'WHERE symbol IN (', TRIM(TRAILING ',' FROM batch_symbols), ') ',
        'OR symbol REGEXP "^(', REPLACE(TRIM(TRAILING ',' FROM batch_symbols), '","', '|'), ')_" ',
        'ON DUPLICATE KEY UPDATE ',
        'exchange = VALUES(exchange), datetime = VALUES(datetime), ',
        '`interval` = VALUES(`interval`), volume = VALUES(volume), ',
        'turnover = VALUES(turnover), open_interest = VALUES(open_interest), ',
        'open_price = VALUES(open_price), high_price = VALUES(high_price), ',
        'low_price = VALUES(low_price), close_price = VALUES(close_price)'
      );
      
      PREPARE stmt FROM @sql_query;
      EXECUTE stmt;
      DEALLOCATE PREPARE stmt;
      
      -- 重置批次计数器
      SET batch_count = 0;
      SET batch_symbols = '';
    END IF;
    
  END LOOP;
  
  CLOSE symbol_cursor;
END//
DELIMITER ;

-- 调用存储过程
CALL CopySymbolData();

-- 清理存储过程
DROP PROCEDURE CopySymbolData;

-- 步骤3：重新生成overview数据（使用SQL而非ORM）
INSERT INTO vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
(symbol, exchange, `interval`, `count`, `start`, `end`)
SELECT
    symbol,
    exchange,
    `interval`,
    COUNT(*) AS `count`,
    MIN(datetime) AS `start`,
    MAX(datetime) AS `end`
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbardata
WHERE symbol IN (SELECT DISTINCT symbol FROM temp_test_symbols)
   OR symbol REGEXP CONCAT('^(',
       (SELECT GROUP_CONCAT(DISTINCT symbol SEPARATOR '|') FROM temp_test_symbols),
       ')_')
GROUP BY symbol, exchange, `interval`
ON DUPLICATE KEY UPDATE
    `count` = VALUES(`count`),
    `start` = VALUES(`start`),
    `end` = VALUES(`end`);

-- 清理临时表
DROP TEMPORARY TABLE temp_symbols_to_delete;

SET foreign_key_checks = 1;
SET autocommit = 1;

-- 使用说明：
-- 1. 此脚本会分批删除带_的symbol记录，避免行锁数量超限
-- 2. 分批复制temp_test_symbols中的数据，每批处理10个symbol
-- 3. 重复索引会被覆盖（使用ON DUPLICATE KEY UPDATE）
-- 4. 使用SQL语句生成overview而非ORM，提高性能
-- 5. symbol匹配使用精确的_连接（无需转义符）

-- 执行前请确认：
-- 1. temp_test_symbols表存在且有数据
-- 2. 两个数据库都可访问
-- 3. 有足够的权限执行删除和插入操作