{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Contract details\n", "-------------------"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-09-07T13:50:30.233331Z", "start_time": "2025-09-07T13:50:30.102594Z"}}, "source": ["from ib_async import *\n", "\n", "util.startLoop()\n", "\n", "import logging\n", "# util.logToConsole(logging.DEBUG)"], "outputs": [], "execution_count": 1}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-09-07T13:50:30.264035Z", "start_time": "2025-09-07T13:50:30.249358Z"}}, "source": ["ib = IB()\n", "# ib.connect('127.0.0.1', 7497, clientId=11)\n", "# ib.connect('47.251.77.10', 4002, clientId=1)\n", "# 47.242.************"], "outputs": [], "execution_count": 2}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-09-07T13:50:32.783851Z", "start_time": "2025-09-07T13:50:30.271161Z"}}, "source": "ib.connect('47.242.117.184', 4011, clientId=401)", "outputs": [{"data": {"text/plain": ["<IB connected to 47.242.117.184:4011 clientId=401>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["Error 200, reqId 6: No security definition has been found for the request, contract: Stock(symbol='PSYGD')\n"]}], "execution_count": 3}, {"cell_type": "markdown", "metadata": {}, "source": ["Suppose we want to find the contract details for AMD stock.\n", "Let's create a stock object and request the details for it:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2025-06-15T02:41:07.093041Z", "start_time": "2025-06-15T02:41:06.103346Z"}}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# amd = Stock('AMD')\n", "amd = Stock('600519')\n", "\n", "cds = ib.reqContractDetails(amd)\n", "\n", "len(cds)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2025-06-15T02:41:09.153617Z", "start_time": "2025-06-15T02:41:08.752767Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}, {"data": {"text/plain": ["[ContractDetails(contract=Contract(secType='STK', conId=744396773, symbol='GMM', exchange='NASDAQ', primaryExchange='NASDAQ', currency='USD', localSymbol='GMM', tradingClass='SCM'), marketName='SCM', minTick=0.0001, orderTypes='ACTIVETIM,AD,ADJUST,ALERT,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,GAT,GTC,GTD,GTT,HID,IOC,LIT,LMT,LOC,MIT,MKT,MOC,MTL,NGCOMB,NONALGO,OCA,OPG,PEGBENCH,RELPCTOFS,RTH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF', validExchanges='SMART,AMEX,NYSE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX', priceMagnifier=1, underConId=0, longName='GLOBAL MOFY AI LTD-A', contractMonth='', industry='Technology', category='Software', subcategory='Computer Software', timeZoneId='US/Eastern', tradingHours='20250614:CLOSED;20250615:CLOSED;20250616:0400-20250616:2000;20250617:0400-20250617:2000;20250618:0400-20250618:2000', liquidHours='20250614:CLOSED;20250615:CLOSED;20250616:0930-20250616:1600;20250617:0930-20250617:1600;20250618:0930-20250618:1600', evRule='', evMultiplier=0, mdSizeMultiplier=1, aggGroup=1, underSymbol='', underSecType='', marketRuleIds='557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557', secIdList=[TagValue(tag='ISIN', value='KYG3937M1143')], realExpirationDate='', lastTradeTime='', stockType='COMMON', minSize=0.0001, sizeIncrement=0.0001, suggestedSizeIncrement=100.0, cusip='', ratings='', descAppend='', bondType='', couponType='', callable=False, putable=False, coupon=0, convertible=False, maturity='', issueDate='', nextOptionDate='', nextOptionType='', nextOptionPartial=False, notes='')]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["amd = Stock('GMM', primaryExchange='NASDAQ')\n", "# amd = Stock('600519')\n", "\n", "cds = ib.reqContractDetails(amd)\n", "print(len(cds))\n", "cds"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-09-07T13:50:39.143631Z", "start_time": "2025-09-07T13:50:38.769169Z"}}, "source": ["# aapls = Stock('AAPL')\n", "# amd = Stock('600519')\n", "# aapl = Stock(conId=265598)\n", "aapl = Stock(conId=779041446)\n", "aapl = ib.reqContractDetails(aapl)[0]\n", "# ib.qualifyContracts(aapl)\n", "aapl\n", "# for v in dir(aapl):\n", "#     if not v.startswith('_'):\n", "#         print(f'{v}: {getattr(aapl, v)}')"], "outputs": [{"data": {"text/plain": ["ContractDetails(contract=Contract(secType='STK', conId=779041446, symbol='PSYGF', exchange='SMART', primaryExchange='PINK', currency='USD', localSymbol='PSYGF', tradingClass='LIMITED'), marketName='LIMITED', minTick=0.0001, orderTypes='ACTIVETIM,AD,ADJUST,ALERT,ALLOC,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LOC,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,POSTONLY,PREOPGRTH,PRICECHK,REL,REL<PERSON>TOFS,SCALE,SCALEODD,SCALERST,SIZECHK,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF', validExchanges='SMART,OTCLNKECN', priceMagnifier=1, underConId=0, longName='PSYENCE GROUP INC', contractMonth='', industry='Consumer, Non-cyclical', category='Pharmaceuticals', subcategory='Medical-Drugs', timeZoneId='US/Eastern', tradingHours='20250907:CLOSED;20250908:0800-20250908:1600;20250909:0800-20250909:1600;20250910:0800-20250910:1600;20250911:0800-20250911:1600;20250912:0800-20250912:1600', liquidHours='20250907:CLOSED;20250908:0800-20250908:1600;20250909:0800-20250909:1600;20250910:0800-20250910:1600;20250911:0800-20250911:1600;20250912:0800-20250912:1600', evRule='', evMultiplier=0, mdSizeMultiplier=1, aggGroup=1, underSymbol='', underSecType='', marketRuleIds='557,557', secIdList=[TagValue(tag='ISIN', value='CA74449Q2053')], realExpirationDate='', lastTradeTime='', stockType='COMMON', minSize=1.0, sizeIncrement=1.0, suggestedSizeIncrement=100.0, cusip='', ratings='', descAppend='', bondType='', couponType='', callable=False, putable=False, coupon=0, convertible=False, maturity='', issueDate='', nextOptionDate='', nextOptionType='', nextOptionPartial=False, notes='')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-09-07T13:51:16.950982Z", "start_time": "2025-09-07T13:51:16.293027Z"}}, "cell_type": "code", "source": ["# aapls = Stock('AAPL')\n", "aapl = Stock('PSYGF') # PSYGD\n", "# aapl = Stock(conId=265598)\n", "# aapl = Stock(conId=543283560)\n", "aapl = ib.reqContractDetails(aapl)#[0]\n", "# ib.qualifyContracts(aapl)\n", "aapl\n", "# for v in dir(aapl):\n", "#     if not v.startswith('_'):\n", "#         print(f'{v}: {getattr(aapl, v)}')"], "outputs": [{"data": {"text/plain": ["[ContractDetails(contract=Contract(secType='STK', conId=779041446, symbol='PSYGF', exchange='SMART', primaryExchange='PINK', currency='USD', localSymbol='PSYGF', tradingClass='LIMITED'), marketName='LIMITED', minTick=0.0001, orderTypes='ACTIVETIM,AD,ADJUST,ALERT,ALLOC,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LOC,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,POSTONLY,PREOPGRTH,PRICECHK,REL,REL<PERSON><PERSON><PERSON>,SCALE,SCALEODD,SCALERST,SIZ<PERSON>H<PERSON>,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF', validExchanges='SMART,OTCLNKECN', priceMagnifier=1, underConId=0, longName='PSYENCE GROUP INC', contractMonth='', industry='Consumer, Non-cyclical', category='Pharmaceuticals', subcategory='Medical-Drugs', timeZoneId='US/Eastern', tradingHours='20250907:CLOSED;20250908:0800-20250908:1600;20250909:0800-20250909:1600;20250910:0800-20250910:1600;20250911:0800-20250911:1600;20250912:0800-20250912:1600', liquidHours='20250907:CLOSED;20250908:0800-20250908:1600;20250909:0800-20250909:1600;20250910:0800-20250910:1600;20250911:0800-20250911:1600;20250912:0800-20250912:1600', evRule='', evMultiplier=0, mdSizeMultiplier=1, aggGroup=1, underSymbol='', underSecType='', marketRuleIds='557,557', secIdList=[TagValue(tag='ISIN', value='CA74449Q2053')], realExpirationDate='', lastTradeTime='', stockType='COMMON', minSize=1.0, sizeIncrement=1.0, suggestedSizeIncrement=100.0, cusip='', ratings='', descAppend='', bondType='', couponType='', callable=False, putable=False, coupon=0, convertible=False, maturity='', issueDate='', nextOptionDate='', nextOptionType='', nextOptionPartial=False, notes=''),\n", " ContractDetails(contract=Contract(secType='STK', conId=779041446, symbol='PSYGF', exchange='OTCLNKECN', primaryExchange='PINK', currency='USD', localSymbol='PSYGF', tradingClass='CURRENT'), marketName='CURRENT', minTick=0.0001, orderTypes='', validExchanges='SMART,OTCLNKECN', priceMagnifier=1, underConId=0, longName='PSYENCE GROUP INC', contractMonth='', industry='Consumer, Non-cyclical', category='Pharmaceuticals', subcategory='Medical-Drugs', timeZoneId='US/Eastern', tradingHours='20250907:CLOSED;20250908:0800-20250908:1600;20250909:0800-20250909:1600;20250910:0800-20250910:1600;20250911:0800-20250911:1600;20250912:0800-20250912:1600', liquidHours='20250907:CLOSED;20250908:0800-20250908:1600;20250909:0800-20250909:1600;20250910:0800-20250910:1600;20250911:0800-20250911:1600;20250912:0800-20250912:1600', evRule='', evMultiplier=0, mdSizeMultiplier=1, aggGroup=1, underSymbol='', underSecType='', marketRuleIds='557,557', secIdList=[TagValue(tag='ISIN', value='CA74449Q2053')], realExpirationDate='', lastTradeTime='', stockType='COMMON', minSize=1.0, sizeIncrement=1.0, suggestedSizeIncrement=100.0, cusip='', ratings='', descAppend='', bondType='', couponType='', callable=False, putable=False, coupon=0, convertible=False, maturity='', issueDate='', nextOptionDate='', nextOptionType='', nextOptionPartial=False, notes='')]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T02:09:30.628239Z", "start_time": "2025-06-16T02:09:22.220343Z"}}, "source": ["report_types = [\n", "    # 'ReportsFinSummary',\n", "    #             'ReportsOwnership',\n", "    #             'RESC',\n", "                'ReportSnapshot',\n", "                ]\n", "                # 'CalendarReport'\n", "                # 'ReportsFinStatements',\n", "reports = {report_type: ib.reqFundamentalData(aapl.contract, report_type) for report_type in report_types}"], "outputs": [], "execution_count": 7}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T02:09:33.786082Z", "start_time": "2025-06-16T02:09:33.740710Z"}}, "source": ["ReportSnapshot = reports['ReportSnapshot']\n", "with open('ReportSnapshot.xml', 'w') as f:\n", "    f.write(ReportSnapshot)\n", "ReportSnapshot"], "outputs": [{"data": {"text/plain": ["'<?xml version=\"1.0\" encoding=\"UTF-8\"?>\\r\\n<ReportSnapshot Major=\"1\" Minor=\"0\" Revision=\"1\">\\r\\n\\t<CoIDs>\\r\\n\\t\\t<CoID Type=\"RepNo\">05680</CoID>\\r\\n\\t\\t<CoID Type=\"CompanyName\">Apple Inc</CoID>\\r\\n\\t\\t<CoID Type=\"IRSNo\">*********</CoID>\\r\\n\\t\\t<CoID Type=\"CIKNo\">0000320193</CoID>\\r\\n\\t\\t<CoID Type=\"OrganizationPermID\">4295905573</CoID>\\r\\n\\t</CoIDs>\\r\\n\\t<Issues>\\r\\n\\t\\t<Issue ID=\"1\" Type=\"C\" Desc=\"Common Stock\" Order=\"1\">\\r\\n\\t\\t\\t<IssueID Type=\"Name\">Ordinary Shares</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"Ticker\">AAPL</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"RIC\">AAPL.O</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"DisplayRIC\">AAPL.OQ</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"InstrumentPI\">331724</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"QuotePI\">7645713</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"InstrumentPermID\">8590932301</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"QuotePermID\">55835312773</IssueID>\\r\\n\\t\\t\\t<Exchange Code=\"NASD\" Country=\"USA\">NASDAQ</Exchange>\\r\\n\\t\\t\\t<GlobalListingType>OSR</GlobalListingType>\\r\\n\\t\\t\\t<MostRecentSplit Date=\"2020-08-31\">4.0</MostRecentSplit>\\r\\n\\t\\t</Issue>\\r\\n\\t\\t<Issue ID=\"2\" Type=\"P\" Desc=\"Preferred Stock\" Order=\"1\">\\r\\n\\t\\t\\t<IssueID Type=\"Name\">Preference Shares</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"InstrumentPI\">100112850</IssueID>\\r\\n\\t\\t\\t<IssueID Type=\"InstrumentPermID\">21482886553</IssueID>\\r\\n\\t\\t\\t<Exchange Code=\"NA\" Country=\"\">Not Available</Exchange>\\r\\n\\t\\t</Issue>\\r\\n\\t</Issues>\\r\\n\\t<CoGeneralInfo>\\r\\n\\t\\t<CoStatus Code=\"1\">Active</CoStatus>\\r\\n\\t\\t<CoType Code=\"EQU\">Equity Issue</CoType>\\r\\n\\t\\t<LastModified>2025-06-12</LastModified>\\r\\n\\t\\t<LatestAvailableAnnual>2024-09-28</LatestAvailableAnnual>\\r\\n\\t\\t<LatestAvailableInterim>2025-03-29</LatestAvailableInterim>\\r\\n\\t\\t<Employees LastUpdated=\"2024-09-28\">164000</Employees>\\r\\n\\t\\t<SharesOut Date=\"2025-04-18\" TotalFloat=\"***********.0\">***********.0</SharesOut>\\r\\n\\t\\t<ReportingCurrency Code=\"USD\">U.S. Dollars</ReportingCurrency>\\r\\n\\t\\t<MostRecentExchange Date=\"2025-06-11\">1.0</MostRecentExchange>\\r\\n\\t</CoGeneralInfo>\\r\\n\\t<TextInfo>\\r\\n\\t\\t<Text Type=\"Business Summary\" lastModified=\"2024-11-12T14:08:27\">Apple Inc. designs, manufactures and markets smartphones, personal computers, tablets, wearables and accessories, and sells a variety of related services. Its product categories include iPhone, Mac, iPad, and Wearables, Home and Accessories. Its software platforms include iOS, iPadOS, macOS, watchOS, visionOS, and tvOS. Its services include advertising, AppleCare, cloud services, digital content and payment services. The Company operates various platforms, including the App Store, that allow customers to discover and download applications and digital content, such as books, music, video, games and podcasts. It also offers digital content through subscription-based services, including Apple Arcade, Apple Fitness+, Apple Music, Apple News+, and Apple TV+. Its products include iPhone 16 Pro, iPhone 16, iPhone 15, iPhone 14, iPhone SE, MacBook Air, MacBook Pro, iMac, Mac mini, Mac Studio, Mac Pro, iPad Pro, iPad Air, AirPods, AirPods Pro, AirPods Max, Apple TV and Apple Vision Pro.</Text>\\r\\n\\t\\t<Text Type=\"Financial Summary\" lastModified=\"2025-05-02T20:35:11\"> BRIEF: For the 26 weeks ended 29 March 2025, Apple Inc revenues increased 4% to $219.66B. Net income increased 6% to $61.11B. Revenues reflect Americas segment increase of 6% to $92.96B, Europe segment increase of 7% to $58.32B, Japan segment increase of 16% to $16.29B. Net income benefited from Americas segment income increase of 8% to $38.28B, Europe segment income increase of 10% to $24.92B, Japan segment income increase of 11% to $7.75B.</Text>\\r\\n\\t</TextInfo>\\r\\n\\t<contactInfo lastUpdated=\"2025-06-12T05:17:36\">\\r\\n\\t\\t<streetAddress line=\"1\">One Apple Park Way</streetAddress>\\r\\n\\t\\t<streetAddress line=\"2\"></streetAddress>\\r\\n\\t\\t<streetAddress line=\"3\"></streetAddress>\\r\\n\\t\\t<city>CUPERTINO</city>\\r\\n\\t\\t<state-region>CA</state-region>\\r\\n\\t\\t<postalCode>95014</postalCode>\\r\\n\\t\\t<country code=\"USA\">United States</country>\\r\\n\\t\\t<contactName></contactName>\\r\\n\\t\\t<contactTitle></contactTitle>\\r\\n\\t\\t<phone>\\r\\n\\t\\t\\t<phone type=\"mainphone\">\\r\\n\\t\\t\\t\\t<countryPhoneCode>1</countryPhoneCode>\\r\\n\\t\\t\\t\\t<city-areacode>408</city-areacode>\\r\\n\\t\\t\\t\\t<number>9961010</number>\\r\\n\\t\\t\\t</phone>\\r\\n\\t\\t</phone>\\r\\n\\t</contactInfo>\\r\\n\\t<webLinks lastUpdated=\"2017-06-29T16:30:55\"><webSite mainCategory=\"Home Page\">https://www.apple.com/</webSite><eMail mainCategory=\"Company Contact/E-mail\">\\r\\n\\t\\t</eMail></webLinks>\\r\\n\\t<peerInfo lastUpdated=\"2025-06-12T05:17:36\">\\r\\n\\t\\t<IndustryInfo>\\r\\n\\t\\t\\t<Industry type=\"TRBC\" order=\"1\" reported=\"0\" code=\"5710602011\" mnem=\"\">Phones &amp; Smart Phones</Industry>\\r\\n\\t\\t\\t<Industry type=\"NAICS\" order=\"1\" reported=\"0\" code=\"334220\" mnem=\"\">Radio and Television Broadcasting and Wireless Communications Equipment Manufacturing</Industry>\\r\\n\\t\\t\\t<Industry type=\"NAICS\" order=\"2\" reported=\"0\" code=\"334111\" mnem=\"\">Electronic Computer Manufacturing</Industry>\\r\\n\\t\\t\\t<Industry type=\"NAICS\" order=\"3\" reported=\"0\" code=\"513210\" mnem=\"\">Software Publishers</Industry>\\r\\n\\t\\t\\t<Industry type=\"NAICS\" order=\"4\" reported=\"0\" code=\"334310\" mnem=\"\">Audio and Video Equipment Manufacturing</Industry>\\r\\n\\t\\t\\t<Industry type=\"NAICS\" order=\"5\" reported=\"0\" code=\"334112\" mnem=\"\">Computer Storage Device Manufacturing</Industry>\\r\\n\\t\\t\\t<Industry type=\"NAICS\" order=\"6\" reported=\"0\" code=\"449210\" mnem=\"\">Electronics and Appliance Retailers</Industry>\\r\\n\\t\\t\\t<Industry type=\"SIC\" order=\"0\" reported=\"1\" code=\"3571\" mnem=\"\">Electronic Computers</Industry>\\r\\n\\t\\t\\t<Industry type=\"SIC\" order=\"1\" reported=\"0\" code=\"3663\" mnem=\"\">Radio/tv Communications Equip</Industry>\\r\\n\\t\\t\\t<Industry type=\"SIC\" order=\"2\" reported=\"0\" code=\"3571\" mnem=\"\">Electronic Computers</Industry>\\r\\n\\t\\t\\t<Industry type=\"SIC\" order=\"3\" reported=\"0\" code=\"7372\" mnem=\"\">Prepackaged Software</Industry>\\r\\n\\t\\t\\t<Industry type=\"SIC\" order=\"4\" reported=\"0\" code=\"3651\" mnem=\"\">Household Audio/video Equipment</Industry>\\r\\n\\t\\t\\t<Industry type=\"SIC\" order=\"5\" reported=\"0\" code=\"3572\" mnem=\"\">Computer Storage Devices</Industry>\\r\\n\\t\\t\\t<Industry type=\"SIC\" order=\"6\" reported=\"0\" code=\"5722\" mnem=\"\">Household Appliance Stores</Industry>\\r\\n\\t\\t</IndustryInfo>\\r\\n\\t\\t<Indexconstituet>S&amp;P 500</Indexconstituet>\\r\\n\\t\\t<Indexconstituet>Dow Industry</Indexconstituet>\\r\\n\\t</peerInfo>\\r\\n\\t<officers>\\r\\n\\t\\t<officer rank=\"1\" since=\"03/1998\">\\r\\n\\t\\t\\t<firstName>Timothy</firstName>\\r\\n\\t\\t\\t<mI>D.</mI>\\r\\n\\t\\t\\t<lastName>Cook</lastName>\\r\\n\\t\\t\\t<age>64 </age>\\r\\n\\t\\t\\t<title startYear=\"2011\" startMonth=\"08\" startDay=\"24\" iD1=\"CEO\" abbr1=\"CEO\" iD2=\"DRC\" abbr2=\"Dir.\">Chief Executive Officer, Director</title>\\r\\n\\t\\t</officer>\\r\\n\\t\\t<officer rank=\"2\" since=\"01/01/2025\">\\r\\n\\t\\t\\t<firstName>Kevan</firstName>\\r\\n\\t\\t\\t<mI></mI>\\r\\n\\t\\t\\t<lastName>Parekh</lastName>\\r\\n\\t\\t\\t<age>53 </age>\\r\\n\\t\\t\\t<title startYear=\"2025\" startMonth=\"01\" startDay=\"01\" iD1=\"CFO\" abbr1=\"CFO\" iD2=\"SVP\" abbr2=\"Sr. VP\">Senior Vice President, Chief Financial Officer</title>\\r\\n\\t\\t</officer>\\r\\n\\t\\t<officer rank=\"3\" since=\"07/2010\">\\r\\n\\t\\t\\t<firstName>Jeffrey</firstName>\\r\\n\\t\\t\\t<mI>E.</mI>\\r\\n\\t\\t\\t<lastName>Williams</lastName>\\r\\n\\t\\t\\t<age>61 </age>\\r\\n\\t\\t\\t<title startYear=\"2018\" startMonth=\"11\" startDay=\"13\" iD1=\"COO\" abbr1=\"COO\" iD2=\"\" abbr2=\"\">Chief Operating Officer</title>\\r\\n\\t\\t</officer>\\r\\n\\t\\t<officer rank=\"4\" since=\"11/13/2017\">\\r\\n\\t\\t\\t<firstName>Katherine</firstName>\\r\\n\\t\\t\\t<mI>L.</mI>\\r\\n\\t\\t\\t<lastName>Adams</lastName>\\r\\n\\t\\t\\t<age>60 </age>\\r\\n\\t\\t\\t<title startYear=\"2017\" startMonth=\"11\" startDay=\"13\" iD1=\"SVP\" abbr1=\"Sr. VP\" iD2=\"GCN\" abbr2=\"Counsel\">Senior Vice President, General Counsel, Secretary</title>\\r\\n\\t\\t</officer>\\r\\n\\t\\t<officer rank=\"5\" since=\"02/05/2019\">\\r\\n\\t\\t\\t<firstName>Deirdre</firstName>\\r\\n\\t\\t\\t<mI></mI>\\r\\n\\t\\t\\t<lastName>O\\'Brien</lastName>\\r\\n\\t\\t\\t<age>58 </age>\\r\\n\\t\\t\\t<title startYear=\"2019\" startMonth=\"02\" startDay=\"05\" iD1=\"SVP\" abbr1=\"Sr. VP\" iD2=\"\" abbr2=\"\">Senior Vice President - Retail and People</title>\\r\\n\\t\\t</officer>\\r\\n\\t</officers>\\r\\n\\t<Ratios PriceCurrency=\"USD\" ReportingCurrency=\"USD\" ExchangeRate=\"1.00000\" LatestAvailableDate=\"2025-03-29\">\\r\\n\\t\\t<Group ID=\"Price and Volume\">\\r\\n\\t\\t\\t<Ratio FieldName=\"NPRICE\" Type=\"N\">199.20000</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"NHIG\" Type=\"N\">260.10000</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"NLOW\" Type=\"N\">169.21010</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"PDATE\" Type=\"D\">2025-06-12T00:00:00</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"VOL10DAVG\" Type=\"N\">53.04874</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"EV\" Type=\"N\">3024905.00000</Ratio>\\r\\n\\t\\t</Group>\\r\\n\\t\\t<Group ID=\"Income Statement\">\\r\\n\\t\\t\\t<Ratio FieldName=\"MKTCAP\" Type=\"N\">2975217.00000</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMREV\" Type=\"N\">400366.00000</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMEBITD\" Type=\"N\">138866.00000</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMNIAC\" Type=\"N\">107540.00000</Ratio>\\r\\n\\t\\t</Group>\\r\\n\\t\\t<Group ID=\"Per share data\">\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMEPSXCLX\" Type=\"N\">7.08008</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMREVPS\" Type=\"N\">26.34072</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"QBVPS\" Type=\"N\">4.47116</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"QCSHPS\" Type=\"N\">3.24633</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMCFSHR\" Type=\"N\">7.83196</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMDIVSHR\" Type=\"N\">1.00000</Ratio>\\r\\n\\t\\t</Group>\\r\\n\\t\\t<Group ID=\"Other Ratios\">\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMGROSMGN\" Type=\"N\">46.63208</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMROEPCT\" Type=\"N\">152.54980</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"TTMPR2REV\" Type=\"N\">7.43124</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"PEEXCLXOR\" Type=\"N\">28.13528</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"PRICE2BK\" Type=\"N\">44.55224</Ratio>\\r\\n\\t\\t\\t<Ratio FieldName=\"Employees\" Type=\"N\">164000</Ratio>\\r\\n\\t\\t</Group>\\r\\n\\t</Ratios>\\r\\n\\t<ForecastData ConsensusType=\"Mean\" CurFiscalYear=\"2025\" CurFiscalYearEndMonth=\"9\" CurInterimEndCalYear=\"2025\" CurInterimEndMonth=\"6\" EarningsBasis=\"PRX\">\\r\\n\\t\\t<Ratio FieldName=\"ConsRecom\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">2.1489</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"TargetPrice\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">221.10070</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"ProjLTGrowthRate\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">14.0000</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"ProjPE\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">27.55606</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"ProjSales\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">407239.24760</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"ProjSalesQ\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">88776.71250</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"ProjEPS\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">7.22890</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"ProjEPSQ\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">1.41800</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"ProjProfit\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">108411.77450</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t\\t<Ratio FieldName=\"ProjDPS\" Type=\"N\">\\r\\n\\t\\t\\t<Value PeriodType=\"CURR\">1.03000</Value>\\r\\n\\t\\t</Ratio>\\r\\n\\t</ForecastData>\\r\\n</ReportSnapshot>\\r\\n'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T02:09:43.132054Z", "start_time": "2025-06-16T02:09:43.126920Z"}}, "source": ["# parse XML document\n", "import xml.etree.ElementTree as ET\n", "tree = ET.fromstring(ReportSnapshot)\n", "from time import sleep\n", "def self_iter_print(elem, level=0):\n", "    indent = ' ' * (level * 2)\n", "    print(f'{indent}{elem.tag}: {elem.text}')\n", "    for child in elem:\n", "        self_iter_print(child, level + 1)\n", "        sleep(0.01)  # to avoid overwhelming the output\n", "\n", "# self_iter_print(tree)"], "outputs": [], "execution_count": 10}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T02:09:44.471692Z", "start_time": "2025-06-16T02:09:44.468195Z"}}, "cell_type": "code", "source": ["# tags = [elem.text for elem in tree.findall('.//SharesOut')]\n", "tag = tree.find('.//SharesOut')\n", "print(tag.tag, tag.text)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SharesOut ***********.0\n"]}], "execution_count": 11}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2025-06-15T02:42:38.727822Z", "start_time": "2025-06-15T02:42:38.501250Z"}}, "outputs": [], "source": ["contract = Forex('EURUSD')\n", "ib.qualifyContracts(contract)\n", "ticker = ib.reqMktDepth(contract)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["amd = Future('ES')\n", "\n", "cds = ib.reqContractDetails(amd)\n", "\n", "len(cds)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2024-11-26T06:34:28.728004Z", "start_time": "2024-11-26T06:34:28.398427Z"}}, "outputs": [{"data": {"text/plain": ["ContractDetails(contract=Contract(secType='STK', conId=658821482, symbol='GMM.OLD', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='GMM.OLD', tradingClass='SCM'), marketName='SCM', minTick=0.0001, orderTypes='ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>EL,R<PERSON>2MI<PERSON>,R<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>TH,<PERSON><PERSON><PERSON>,SCALEODD,SCALERST,<PERSON>IZECH<PERSON>,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF', validExchanges='SMART,AMEX,NYSE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX', priceMagnifier=1, underConId=0, longName='GLOBAL MOFY AI LTD-A', contractMonth='', industry='Technology', category='Software', subcategory='Computer Software', timeZoneId='US/Eastern', tradingHours='20241126:0400-20241126:2000;20241127:0400-20241127:2000;20241128:CLOSED;20241129:0400-20241129:1700', liquidHours='20241126:0930-20241126:1600;20241127:0930-20241127:1600;20241128:CLOSED;20241129:0930-20241129:1300', evRule='', evMultiplier=0, mdSizeMultiplier=1, aggGroup=1, underSymbol='', underSecType='', marketRuleIds='557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557,557', secIdList=[TagValue(tag='ISIN', value='KYG3937M1069')], realExpirationDate='', lastTradeTime='', stockType='COMMON', minSize=1.0, sizeIncrement=1.0, suggestedSizeIncrement=100.0, cusip='', ratings='', descAppend='', bondType='', couponType='', callable=False, putable=False, coupon=0, convertible=False, maturity='', issueDate='', nextOptionDate='', nextOptionType='', nextOptionPartial=False, notes='')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["tmp = Contract(conId=658821482)\n", "cds = ib.reqContractDetails(tmp)\n", "\n", "len(cds)\n", "cds[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We get a long list of contract details. Lets print the first one:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cds[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags = cds[0].secIdList\n", "isin = next((tag.value for tag in tags if tag.tag == 'ISIN'), None)\n", "isin"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The contract itself is in the 'contract' property of the contract details. Lets make a list of contracts and look at the first:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["contracts = [cd.contract for cd in cds]\n", "\n", "contracts[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To better spot the difference between all the contracts it's handy to convert to a DataFrame. There is a utility function to do that:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["util.df(contracts)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see from this that AMD trades in different currencies on different exchanges.\n", "Suppose we want the one in USD on the SMART exchange. The AMD contract is adjusted to\n", "reflect that and becomes unique:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["amd = Stock('AMD', 'SMART', 'USD')\n", "\n", "assert len(ib.reqContractDetails(amd)) == 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lets try the same for Intel:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["intc = Stock('INTC', 'SMART', 'USD')\n", "\n", "assert len(ib.reqContractDetails(intc)) == 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's try a non-existing contract:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xxx = Stock('XXX', 'SMART', 'USD')\n", "\n", "assert len(ib.reqContractDetails(xxx)) == 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["or a Forex contract"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eurusd = Forex('EURUSD')\n", "\n", "assert len(ib.reqContractDetails(eurusd)) == 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With the ``qualifyContracts`` method the extra information that is send back\n", "from the contract details request is used to fill in the original contracts.\n", "\n", "Lets do that with ``amd`` and compare before and aftwards:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-06-15T04:34:56.803734Z", "start_time": "2025-06-15T04:34:56.795809Z"}}, "source": ["amd"], "outputs": [{"data": {"text/plain": ["Stock(symbol='GMM', primaryExchange='NASDAQ')"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "execution_count": 29}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-06-15T04:34:58.676932Z", "start_time": "2025-06-15T04:34:58.405293Z"}}, "source": ["ib.qualifyContracts(amd)\n", "amd"], "outputs": [{"data": {"text/plain": ["Stock(conId=744396773, symbol='GMM', exchange='NASDAQ', primaryExchange='NASDAQ', currency='USD', localSymbol='GMM', tradingClass='SCM')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "execution_count": 30}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-15T04:34:58.676932Z", "start_time": "2025-06-15T04:34:58.405293Z"}}, "cell_type": "code", "outputs": [{"data": {"text/plain": ["Stock(conId=744396773, symbol='GMM', exchange='NASDAQ', primaryExchange='NASDAQ', currency='USD', localSymbol='GMM', tradingClass='SCM')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "execution_count": 30, "source": ["aapl = ib.qualifyContracts(amd)\n", "amd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**TIP:** When printing a contract, the output can be copy-pasted and it will be valid Python code.\n", "\n", "The ``conId`` that is returned can by itself be used to uniquely specify a contract:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["contract_4391 = Contract(conId=4391)\n", "\n", "ib.qualifyContracts(contract_4391)"], "outputs": []}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["\n", "assert contract_4391 == amd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A whole bunch of contracts can be qualified at the same time. A list of all the successfull ones is returned:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qualContracts = ib.qualifyContracts(amd, intc, xxx, eurusd)\n", "\n", "assert intc in qualContracts\n", "assert xxx not in qualContracts"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is also an API function to request stocks (only stocks) that match a pattern:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["matches = ib.reqMatchingSymbols('intc')\n", "matchContracts = [m.contract for m in matches]\n", "\n", "matches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert intc in matchContracts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ib.disconnect()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}