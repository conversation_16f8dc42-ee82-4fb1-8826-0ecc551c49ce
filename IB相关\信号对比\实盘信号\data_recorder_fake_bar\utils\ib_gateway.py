"""
IB Symbol Rules

SPY-USD-STK   SMART
EUR-USD-CASH  IDEALPRO
XAUUSD-USD-CMDTY  SMART
ES-202002-USD-FUT  GLOBEX
SI-202006-1000-USD-FUT  NYMEX
ES-2020006-C-2430-50-USD-FOP  GLOBEX

ConId is also supported for symbol.
"""
import math
import sys
from copy import copy
from datetime import datetime, timedelta, time
from threading import Thread, Condition
from typing import Optional, Dict, Union, Any
from decimal import Decimal
import shelve

from numpy import format_float_positional

from ibapi.tag_value import TagValue
from tzlocal import get_localzone_name

from vnpy.event import EventEngine
from ibapi.client import EClient
from ibapi.common import OrderId, TickAttrib, TickerId
from ibapi.contract import Contract, ContractDetails
from ibapi.execution import Execution
from ibapi.order import Order
from ibapi.order_state import OrderState
from ibapi.ticktype import TickType, TickTypeEnum
from ibapi.wrapper import EWrapper
from ibapi.utils import iswrapper
from ibapi.common import BarData as IbBarData
from ibapi.order_cancel import OrderCancel

from vnpy.trader.gateway import BaseGateway
from vnpy.trader.object import (
    TickData,
    OrderData,
    TradeData,
    PositionData,
    AccountData,
    ContractData,
    BarData,
    OrderRequest,
    CancelRequest,
    SubscribeRequest,
    HistoryRequest
)
from vnpy.trader.constant import (
    Product,
    OrderType,
    Direction,
    Exchange,
    Currency,
    Status,
    OptionType,
    Interval
)
from vnpy.trader.utility import get_file_path, ZoneInfo, round_to
from vnpy.trader.event import EVENT_TIMER, EVENT_LOG
from vnpy.event import Event

from .AvailableAlgoParams import AvailableAlgoParams
from .event import EVENT_BAR_MINI, EVENT_BAR_CLOSE, EVENT_TICK_RECORD, EVENT_PRE_CLOSE, EVENT_ORDER_ERROR_RECORD
from .data_types import OrderErrorData

# 美股交易所列表
US_STOCK_EXCHANGES = ('SMART',

                      'AMEX', 'ARCA', 'ARCAEDGE', 'BATS', 'BEX', 'BYX', 'CBOE', 'CHX', 'DRCTEDGE', 'EDGEA', 'EDGX',
                      'IBEOS', 'IEX', 'ISE', 'LTSE', 'MEMX', 'NASDAQ', 'NYSE', 'NYSENAT', 'OTCLNKECN',
                      'PEARL', 'PHLX', 'PSX',
                      'ISLAND', 'PINK', 'NYSEFLOOR'

                      # 'OVERNIGHT', # 夜盘
                      # 'FOXRIVER', # FOX RIVER算法
                      # 'TPLUS0' # T+0 结算
                      )

# 委托状态映射
STATUS_IB2VT: dict[str, Status] = {
    "ApiPending": Status.SUBMITTING,
    "PendingSubmit": Status.SUBMITTING,
    "PreSubmitted": Status.NOTTRADED,
    "Submitted": Status.NOTTRADED,
    "ApiCancelled": Status.CANCELLED,
    "Cancelled": Status.CANCELLED,
    "Filled": Status.ALLTRADED,
    "Inactive": Status.REJECTED,
}

# 多空方向映射
DIRECTION_VT2IB: dict[Direction, str] = {Direction.LONG: "BUY", Direction.SHORT: "SELL"}
DIRECTION_IB2VT: dict[str, Direction] = {v: k for k, v in DIRECTION_VT2IB.items()}
DIRECTION_IB2VT["BOT"] = Direction.LONG
DIRECTION_IB2VT["SLD"] = Direction.SHORT

# 委托类型映射
ORDERTYPE_VT2IB: dict[OrderType, str] = {
    OrderType.LIMIT: "LMT",
    OrderType.MARKET: "MKT",
    OrderType.STOP: "STP"
}
ORDERTYPE_IB2VT: dict[str, OrderType] = {v: k for k, v in ORDERTYPE_VT2IB.items()}

# 交易所映射，与原版差异：
# Exchange.OTC: "PINK"
EXCHANGE_VT2IB: dict[Exchange, str] = {exchange:exchange.value for exchange in Exchange}

EXCHANGE_IB2VT: dict[str, Exchange] = {v: k for k, v in EXCHANGE_VT2IB.items()}

# 产品类型映射
PRODUCT_IB2VT: dict[str, Product] = {
    "STK": Product.EQUITY,
    "CASH": Product.FOREX,
    "CMDTY": Product.SPOT,
    "FUT": Product.FUTURES,
    "OPT": Product.OPTION,
    "FOP": Product.OPTION,
    "CONTFUT": Product.FUTURES,
    "IND": Product.INDEX,
    "CFD": Product.CFD
}

# 期权类型映射
OPTION_IB2VT: dict[str, OptionType] = {
    "C": OptionType.CALL,
    "CALL": OptionType.CALL,
    "P": OptionType.PUT,
    "PUT": OptionType.PUT
}

# 货币类型映射
CURRENCY_VT2IB: dict[Currency, str] = {
    Currency.USD: "USD",
    Currency.CAD: "CAD",
    Currency.CNY: "CNY",
    Currency.HKD: "HKD",
}

# 切片数据字段映射
TICKFIELD_IB2VT: dict[int, str] = {
    # tickPrice回调更新的字段
    TickTypeEnum.BID: "bid_price_1",          # 买一价
    TickTypeEnum.ASK: "ask_price_1",          # 卖一价
    TickTypeEnum.LAST: "last_price",          # 最新成交价
    TickTypeEnum.HIGH: "high_price",          # 最高价
    TickTypeEnum.LOW: "low_price",           # 最低价
    TickTypeEnum.OPEN: "open_price",         # 开盘价
    TickTypeEnum.CLOSE: "pre_close",         # 昨收价

    # tickSize回调更新的字段
    TickTypeEnum.BID_SIZE: "bid_volume_1",    # 买一量
    TickTypeEnum.ASK_SIZE: "ask_volume_1",    # 卖一量
    TickTypeEnum.LAST_SIZE: "last_volume",    # 最新成交量
    TickTypeEnum.VOLUME: "volume",           # 总成交量 # 比RT Trade Volume大，不可用
    TickTypeEnum.FUTURES_OPEN_INTEREST: "open_interest",  # 持仓量

    # tickString回调更新的字段
    TickTypeEnum.LAST_TIMESTAMP: "datetime",  # 最后成交时间
    TickTypeEnum.RT_TRD_VOLUME: "last_price;last_volume;volume;datetime;_vwap;_single_market_maker",  # 实时成交数据

    # tickGeneric回调更新的字段
    TickTypeEnum.HALTED: "halted",           # 是否停牌

    # tickOptionComputation回调期权相关字段
    TickTypeEnum.BID_OPTION_COMPUTATION: "bid",      # 期权买价
    TickTypeEnum.ASK_OPTION_COMPUTATION: "ask",      # 期权卖价
    TickTypeEnum.LAST_OPTION_COMPUTATION: "last",    # 期权最新价
    TickTypeEnum.MODEL_OPTION: "model",             # 期权模型价
}

# 账户类型映射
ACCOUNTFIELD_IB2VT: dict[str, str] = {
    "NetLiquidationByCurrency": "balance",
    "NetLiquidation": "balance",
    "UnrealizedPnL": "positionProfit",
    "AvailableFunds": "available",
    "MaintMarginReq": "margin",
}

# 数据频率映射
INTERVAL_VT2IB: dict[Interval, str] = {
    Interval.MINUTE: "1 min",
    Interval.HOUR: "1 hour",
    Interval.DAILY: "1 day",
}

# 其他常量
localzone_name = get_localzone_name()
LOCAL_TZ = ZoneInfo(localzone_name)
EASTERN_TZ = ZoneInfo('US/Eastern')
CENTRAL_TZ = ZoneInfo('US/Central')
UTC_TZ = ZoneInfo('UTC')
JOIN_SYMBOL: str = "/"

# Trading period mapping for different security types
SECTYPE_TRADING_PERIOD = {
    'STK': 'liquidHours',
    'FUT': 'tradingHours',
    'OPT': 'tradingHours',
    'IND': 'tradingHours'
}

# Add after other constants
US_STOCK_START = time(9, 30)
US_STOCK_END = time(16, 0)


class IbGateway(BaseGateway):
    """
    VeighNa用于对接IB的交易接口。
    """

    default_name: str = "IB"

    default_setting: dict = {
        "TWS地址": "localhost",
        "TWS端口": 4002,
        "客户号": 1,
        "交易账户": ""
    }

    exchanges: list[str] = list(EXCHANGE_VT2IB.keys())
    tradinghours_info: Dict[str, dict] = {} # 移动到类级别以实现共享

    def __init__(self, event_engine: EventEngine, gateway_name: str) -> None:
        """构造函数"""
        super().__init__(event_engine, gateway_name)

        self.api: "IbApi" = IbApi(self)
        self.count: int = 0
        
        # Gateway mode (0: normal, 1: market data only, -1: trading only)
        self.quote_only: int = 0

        # 是否使用5秒bar
        self.use_5s_bar: bool = True

    def connect(self, setting: dict) -> None:
        """连接交易接口"""
        host: str = setting["TWS地址"]
        port: int = setting["TWS端口"]
        clientid: int = setting["客户号"]
        account: str = setting["交易账户"]

        self.api.connect(host, port, clientid, account)

        self.event_engine.register(EVENT_TIMER, self.process_timer_event)

    def close(self) -> None:
        """关闭接口"""
        self.event_engine.unregister(EVENT_TIMER, self.process_timer_event)
        self.api.close()

    def subscribe(self, req: SubscribeRequest) -> None:
        """订阅行情"""
        self.api.subscribe(req)

    def unsubscribe(self, req: SubscribeRequest) -> None:
        """订阅行情"""
        self.api.unsubscribe(req)

    def send_order(self, req: OrderRequest) -> str:
        """委托下单"""
        return self.api.send_order(req)

    def cancel_order(self, req: CancelRequest) -> None:
        """委托撤单"""
        self.api.cancel_order(req)

    def cancel_all(self) -> None:
        """撤销所有活跃合约的挂单"""
        self.api.cancel_all()

    def query_account(self) -> None:
        """查询资金"""
        pass

    def query_position(self) -> None:
        """查询持仓"""
        self.api.query_position()

    def query_history(self, req: HistoryRequest) -> list[BarData]:
        """查询历史数据"""
        return self.api.query_history(req)

    def process_timer_event(self, event: Event) -> None:
        """定时事件处理"""
        self.count += 1
        if self.count < 10:
            return
        self.count = 0

        self.api.check_connection()
    
        # 检查每个合约的收盘状态
        current = datetime.now(LOCAL_TZ)
        
        for vt_symbol, info in self.tradinghours_info.items():
            try:

                # 检查每个交易时段的收盘时间
                for _, end_time in info['tradingPeriod']:
                    # 如果已经发送过该收盘时间的事件，跳过
                    if info['closingStates'].get(end_time, True):
                        continue
                    end_time_dt = end_time  # 现在已经是datetime对象
                    time_diff = (current - end_time_dt).total_seconds()
                    if 10 < time_diff < 60:
                        # 发送收盘事件
                        self.on_bar_close(vt_symbol)
                        self.write_log(f"Sending closing event for {vt_symbol} at current time: {current} ({current.tzinfo}), end time: {end_time_dt} ({end_time_dt.tzinfo}), time diff: {time_diff}")
                        # 标记该收盘时间已处理
                        info['closingStates'][end_time] = True
            
            except Exception as e:
                self.write_log(f"Error processing closing time for {vt_symbol}: {str(e)}")

    def on_bar_mini(self, bar: BarData) -> None:
        """
        Bar event push.
        """
        self.on_event(EVENT_BAR_MINI, bar)
        # self.on_event(EVENT_BAR_MINI + bar.vt_symbol, bar)

    def on_bar_close(self, vt_symbol: str) -> None:
        """
        Bar close event push.
        """
        self.on_event(EVENT_BAR_CLOSE, vt_symbol)

    def on_tick_record(self, tick: TickData) -> None:
        """
        Tick record event push.
        """
        self.on_event(EVENT_TICK_RECORD, tick)

    def on_pre_close(self, tick: TickData) -> None:
        """
        Pre close tick event push.
        """
        self.on_event(EVENT_PRE_CLOSE, tick)

    def on_event(self, type: str, data: Any = None) -> None:
        """
        根据网关模式过滤事件推送:
        - quote_only = 0: 推送所有事件
        - quote_only = 1: 只推送行情相关事件
        - quote_only = -1: 只推送交易相关事件
        """
        is_market_data = type == EVENT_BAR_CLOSE or isinstance(data, (TickData, BarData, ContractData))

        if (
            self.quote_only == 0
            or (self.quote_only == 1 and is_market_data)
            or (self.quote_only == -1 and not is_market_data)
            or type == EVENT_LOG
        ):
            super().on_event(type, data)

    def on_tick(self, tick: TickData) -> None:
        super().on_tick(tick)
        # self.write_log(f"{tick.__class__.__name__}({', '.join(f'{k}={v}' for k, v in tick.__dict__.items() if v)})")

    def on_contract(self, contract: ContractData) -> None:
        contract.gateway_name = self.default_name
        super().on_contract(contract)
        # self.write_log(f'vt_symbol:{contract.vt_symbol}')

    def write_log(self, msg: str) -> None:
        """输出日志"""
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        line_no = frame.f_lineno
        class_name = self.__class__.__name__
        formatted_msg = f"[{self.gateway_name}.{class_name}.{func_name}:{line_no}] {msg}"
        super().write_log(formatted_msg)

    def data_invalid(self, data: Union[TickData, BarData]) -> bool:
        """Check if data is outside trading hours (直接用datetime对象比较)"""
        try:
            # 如果没有交易时段信息，则认为数据有效
            trading_periods = self.tradinghours_info[data.vt_symbol]['tradingPeriod']
            if not trading_periods:
                return False
            data_dt = data.datetime
            if not data.datetime.tzinfo:
                data_dt = data.datetime.replace(tzinfo=LOCAL_TZ)
            for start_time, end_time in trading_periods:
                if start_time <= data_dt < end_time:
                    return False
        except Exception as e:
            self.write_log(f"data_invalid error: {e}")
            return True

        return True

    def update_trading_hours(self, vt_symbol: str, contract_details: ContractDetails) -> None:
        """Update trading hours info when receiving contract details

        eg:
        contract_details.timeZoneId='US/Eastern'
        contract_details.tradingHours='20250722:0400-20250722:2000;20250723:0400-20250723:2000;20250724:0400-20250724:2000;20250725:0400-20250725:2000'
        contract_details.liquidHours='20250722:0930-20250722:1600;20250723:0930-20250723:1600;20250724:0930-20250724:1600;20250725:0930-20250725:1600'
        """
        # 检查时区是否为美东时间
        if (contract_details.contract.exchange == "SMART" and
                contract_details.timeZoneId != 'US/Eastern'):
            self.write_log(f"Warning: {vt_symbol} timezone is {contract_details.timeZoneId}, not US/Eastern")

        trading_period_name = SECTYPE_TRADING_PERIOD.get(contract_details.contract.secType, 'liquidHours')
        trading_period = (getattr(contract_details, trading_period_name, '') or
                          getattr(contract_details, 'tradingHours',''))

        time_zone_id = getattr(contract_details, 'timeZoneId', localzone_name)
        time_zone = ZoneInfo(time_zone_id)

        tradingPeriod = []
        for elem in trading_period.split(';'):
            if elem and 'CLOSED' not in elem:
                dt, end = elem.split('-')
                # dt, end: 20250722:0930  20250722:1600
                dt_date, dt_hm = dt.split(':')
                end_date, end_hm = end.split(':')
                dt_hour, dt_minute = int(dt_hm[:2]), int(dt_hm[2:])
                end_hour, end_minute = int(end_hm[:2]), int(end_hm[2:])
                try:
                    start_dt = datetime(int(dt_date[:4]), int(dt_date[4:6]), int(dt_date[6:]), dt_hour, dt_minute, tzinfo=time_zone)
                    end_dt = datetime(int(end_date[:4]), int(end_date[4:6]), int(end_date[6:]), end_hour, end_minute, tzinfo=time_zone)
                    # SMART交易所用US_STOCK_START/END限制，整体比较
                    if contract_details.contract.exchange == "SMART":
                        stock_start_dt = datetime(start_dt.year, start_dt.month, start_dt.day, US_STOCK_START.hour, US_STOCK_START.minute, tzinfo=time_zone)
                        stock_end_dt = datetime(end_dt.year, end_dt.month, end_dt.day, US_STOCK_END.hour, US_STOCK_END.minute, tzinfo=time_zone)
                        # 只缩小区间
                        if start_dt < stock_start_dt:
                            start_dt = stock_start_dt
                        if end_dt > stock_end_dt:
                            end_dt = stock_end_dt
                    tradingPeriod.append((start_dt, end_dt))
                except Exception as e:
                    self.write_log(f"tradingPeriod parse error: {e}, {dt}, {end}, tz={time_zone}")

        # 初始化收盘状态字典，key为end_dt
        closing_states = {end_dt: False for _, end_dt in tradingPeriod}

        self.tradinghours_info[vt_symbol] = {
            'tradingPeriod': tradingPeriod,
            'timeZoneId': time_zone_id,
            'closingStates': closing_states  # 添加收盘状态跟踪
        }
        # {'12087792.IDEALPRO': {'tradingPeriod': [(datetime.datetime(2025, 7, 21, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 22, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 22, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 23, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 23, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 24, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 24, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 25, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 27, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 28, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')))], 'timeZoneId': 'US/Eastern', 'closingStates': {datetime.datetime(2025, 7, 22, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 23, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 24, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 25, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 28, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False}},
        # '265598.SMART': {'tradingPeriod': [(datetime.datetime(2025, 7, 22, 9, 30, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 22, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 23, 9, 30, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 23, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 24, 9, 30, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 24, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 25, 9, 30, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 25, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')))], 'timeZoneId': 'US/Eastern', 'closingStates': {datetime.datetime(2025, 7, 22, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 23, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 24, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 25, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False}}}

        self.write_log(f"更新交易时间信息: {vt_symbol}, {trading_period}, {self.tradinghours_info[vt_symbol]}")

class IbApi(EWrapper):
    """IB的API接口"""

    data_filename: str = "ib_contract_data.db"
    data_filepath: str = str(get_file_path(data_filename))
    contracts: dict[str, ContractData] = {} # 移动到类级别以实现共享
    ib_contracts: dict[str, Contract] = {} # 移动到类级别以实现共享
    halted_status: dict[str, int] = {}  # 移动到类级别
    status_desc = { # 移动到类级别
        -1: "状态不可用",
        0: "正常交易",
        1: "一般性停牌",
        2: "波动性停牌"
    }

    def __init__(self, gateway: IbGateway) -> None:
        """构造函数"""
        super().__init__()

        self.gateway: IbGateway = gateway
        self.gateway_name: str = gateway.gateway_name

        self.status: bool = False

        self.reqid: int = 0
        self.orderid: int = 0
        self.clientid: int = 0
        self.history_reqid: int = 0
        self.account: str = ""

        self.ticks: dict[int, TickData] = {}
        self.orders: dict[str, OrderData] = {}
        self.accounts: dict[str, AccountData] = {}
        # self.contracts: dict[str, ContractData] = {} # 移除实例级别初始化
        # self.ib_contracts: dict[str, Contract] = {} # 移除实例级别初始化
        self.contracts_details: dict[str, ContractDetails] = {}

        self.subscribed: dict[str, SubscribeRequest] = {}
        self.data_ready: bool = False
        self.order_ready: bool = False

        self.history_req: HistoryRequest = None
        self.history_condition: Condition = Condition()
        self.history_buf: list[BarData] = []

        self.reqid_symbol_map: dict[int, str] = {}              # reqid: subscribe tick symbol
        self.reqid_underlying_map: dict[int, Contract] = {}     # reqid: query option underlying

        self.client: EClient = EClient(self)

        self.realtime_bars: dict[int, BarData] = {}  # 存储5s bar的缓存

        # 添加跳过标记字典
        self.skip_next_ticksize: dict[int, bool] = {} # reqId: 是否跳过此次tickSize

    def connectAck(self) -> None:
        """连接成功回报"""
        super().connectAck()
        self.status = True
        self.gateway.write_log("IB TWS连接成功")

        # 由于加载合约信息后，会发送on_contract事件，该事件会促使类似datarecorder订阅行情，但现在刚连接上，并不一定是马上订阅行情的好时机
        self.load_contract_data()

        """  
        Code:2104
        TWS message：Market data farm connection is OK。 
        Additional notes：notification that connection to the market data server is ok. This is a notification and not a true error condition, and is expected on first establishing connection.
        
        Important: The IBApi.EWrapper.nextValidID callback is commonly used to indicate that the connection is completed and other messages can be sent from the API client to TWS. 
        There is the possibility that function calls made prior to this time could be dropped by TWS
        """
        self.data_ready = False
        self.order_ready = False

        # 未订阅：3 延时数据，4 延时冻结数据
        # 订阅：1 实时数据
        # self.client.reqMarketDataType(3)

    def connectionClosed(self) -> None:
        """连接断开回报"""
        super().connectionClosed()
        self.status = False
        self.gateway.write_log("IB TWS连接断开")
        
        # 发送连接断开错误记录事件
        order_error = OrderErrorData(
            symbol="CONNECTION",
            exchange=Exchange.SMART,
            error_code=13,
            error_msg="IB TWS连接断开",
            orderid="CONNECTION_CLOSED",
            create_date=datetime.now(),
            gateway_name=self.gateway_name,
            ext1=f"{getattr(self, 'host', 'unknown')}:{getattr(self, 'port', 'unknown')}:{getattr(self, 'clientid', 'unknown')}"
        )
        
        # 发送错误记录事件
        self.gateway.event_engine.put(Event(EVENT_ORDER_ERROR_RECORD, order_error))

    def nextValidId(self, orderId: int) -> None:
        """下一个有效订单号回报"""
        super().nextValidId(orderId)

        self.client.reqCurrentTime()

        if not self.orderid:
            self.orderid = orderId

        if not self.order_ready:
            self.order_ready = True

        # if not self.data_ready:
        #     self.data_ready = True

    def currentTime(self, time: int) -> None:
        """IB当前服务器时间回报"""
        super().currentTime(time)

        dt: datetime = datetime.fromtimestamp(time)
        time_string: str = dt.strftime("%Y-%m-%d %H:%M:%S.%f")

        msg: str = f"服务器时间: {time_string}"
        self.gateway.write_log(msg)

    def error(self, reqId: TickerId, errorCode: int, errorString: str, advancedOrderRejectJson: str = "") -> None:
        """具体错误请求回报"""
        super().error(reqId, errorCode, errorString)

        # 2000-2999信息通知不属于报错信息
        if errorCode not in range(2000, 3000):
            # 如果是历史数据请求的reqId，唤醒等待
            if reqId == self.history_reqid:
                self.history_condition.acquire()
                self.history_condition.notify()
                self.history_condition.release()

            # 尝试从reqId获取相关的合约信息
            symbol = ""
            exchange = Exchange.SMART
            orderid = ""
            
            # 如果reqId对应订单，尝试获取订单信息
            if str(reqId) in self.orders:
                order = self.orders[str(reqId)]
                symbol = order.symbol
                exchange = order.exchange
                orderid = order.orderid
            # 如果reqId对应tick订阅，尝试获取合约信息
            elif reqId in self.ticks:
                tick = self.ticks[reqId]
                symbol = tick.symbol
                exchange = tick.exchange
            # 如果reqId对应合约查询，尝试从映射中获取
            elif reqId in self.reqid_symbol_map:
                symbol = self.reqid_symbol_map[reqId]
            
            # 如果没有获取到symbol，使用reqId作为标识
            if not symbol:
                symbol = f"reqId_{reqId}"
            
            # 创建错误数据对象
            order_error = OrderErrorData(
                symbol=symbol,
                exchange=exchange,
                error_code=errorCode,
                error_msg=errorString,
                orderid=orderid or str(reqId),
                create_date=datetime.now(),
                gateway_name=self.gateway_name,
                ext1=f"{getattr(self, 'host', 'unknown')}:{getattr(self, 'port', 'unknown')}:{getattr(self, 'clientid', 'unknown')}"
            )
            
            # 发送错误记录事件
            self.gateway.event_engine.put(Event(EVENT_ORDER_ERROR_RECORD, order_error))


        msg: str = f"信息通知，代码：{errorCode}，内容: {errorString}"
        self.gateway.write_log(msg)

        '''
        300	Can't find EId with ticker Id:	An attempt was made to cancel market data for a ticker ID that was not associated with a current subscription. With the DDE API this occurs by clearing the spreadsheet cell.
        
        420	Invalid real-time query.	Information about pacing violations
        
            Important: real time bars subscriptions combine the limitations of both, top and historical market data. Make sure you observe Market Data Lines and Pacing Violations for Small Bars (30 secs or less). For example, no more than 60 *new* requests for real time bars can be made in 10 minutes, and the total number of active active subscriptions of all types cannot exceed the maximum allowed market data lines for the user.

            Pacing Violations for Small Bars (30 secs or less)
                Although Interactive Brokers offers our clients high quality market data, IB is not a specialised market data provider and as such it is forced to put in place restrictions to limit traffic which is not directly associated to trading. A Pacing Violation1 occurs whenever one or more of the following restrictions is not observed:
                
                Making identical historical data requests within 15 seconds.
                Making six or more historical data requests for the same Contract, Exchange and Tick Type within two seconds.
                Making more than 60 requests within any ten minute period.
                Note that when BID_ASK historical data is requested, each request is counted twice. In a nutshell, the information above can simply be put as "do not request too much data too quick".
                
                Important: the above limitations apply to all our clients and it is not possible to overcome them. If your trading strategy's market data requirements are not met by our market data services please consider contacting a specialised provider.

        '''

        '''
        
        502	Couldn't connect to TWS. Confirm that "Enable ActiveX and Socket Clients" is enabled and connection port is the same as "Socket Port" on the TWS "Edit->Global Configuration...->API->Settings" menu.	When you receive this error message it is either because you have not enabled API connectivity in the TWS and/or you are trying to connect on the wrong port. Refer to the TWS' API Settings as explained in the error message. See also Connectivity
        
        1100:Connectivity between IB and the TWS has been lost.Your TWS/IB Gateway has been disconnected from IB servers. This can occur because of an internet connectivity issue, a nightly reset of the IB servers, or a competing session.
        1101:Connectivity between IB and TWS has been restored- data lost.*.The TWS/IB Gateway has successfully reconnected to IB's servers. Your market data requests have been lost and need to be re-submitted.
        1102:Connectivity between IB and TWS has been restored- data maintained.The TWS/IB Gateway has successfully reconnected to IB's servers. Your market data requests have been recovered and there is no need for you to re-submit them.        
        '''
        # TWS与IB服务器已经断线
        if errorCode == 1100:
            self.order_ready = False
            self.data_ready = False

        # TWS与IB服务器已经重连，需要重新订阅行情
        if errorCode == 1101:
            self.order_ready = True
            self.data_ready = True
            self.resubscribe_all()

        # TWS与IB服务器已经重连，不需要做任何事情
        if errorCode == 1102:
            self.order_ready = True

            # if not self.data_ready:
            #     self.data_ready = True
            #
            #     # 美东时间早上7点盘前交易期间IB服务器与TWS重连，目前不交易盘前，保险起见重新订阅行情
            #     self.gateway.write_log("行情服务器成功重连，重新订阅行情")
            #     self.resubscribe_all()
        '''
        2103	A market data farm is disconnected.	Indicates a connectivity problem to an IB server. Outside of the nightly IB server reset, this typically indicates an underlying ISP connectivity issue.
        2104	Market data farm connection is OK	A notification that connection to the market data server is ok. This is a notification and not a true error condition, and is expected on first establishing connection.
        2105	A historical data farm is disconnected.	Indicates a connectivity problem to an IB server. Outside of the nightly IB server reset, this typically indicates an underlying ISP connectivity issue.
        2106	A historical data farm is connected.	A notification that connection to the market data server is ok. This is a notification and not a true error condition, and is expected on first establishing connection.
        2107	A historical data farm connection has become inactive but should be available upon demand.	Whenever a connection to the historical data farm is not being used because there is not an active historical data request, the connection will go inactive in IB Gateway. This does not indicate any connectivity issue or problem with IB Gateway. As soon as a historical data request is made the status will change back to active.
        
        2119	Market data farm is connecting.
                
        Sec-def是指Security definition，即合约定义，包括合约的交易所、合约的类型、合约的到期日、合约的执行价格等。
        2157    Sec-def data farm connection is broken
        2158	Sec-def data farm connection is OK	A notification that connection to the Security definition data server is ok. This is a notification and not a true error condition, and is expected on first establishing connection.
        '''
        if errorCode == 502: # 不能用2103，因为盘中可能短暂断连；502出现的情况：重启TWS
            self.data_ready = False
            self.gateway.write_log("行情服务器连接断开")

        # 行情服务器已连接
        if errorCode == 2104 and not self.data_ready:
            self.data_ready = True
            self.gateway.write_log("行情服务器连接成功，订阅行情")

            self.client.reqCurrentTime()
            self.resubscribe_all()

        '''
        10225	Bust event occurred, current subscription is deactivated. Please resubscribe real-time bars immediately	
        '''
        # 处理10225错误 - 重新订阅realtime bars
        if errorCode == 10225:
            self.resubscribe_all()

    def resubscribe_all(self) -> None:
        """重新订阅所有行情数据，包括tick和realtime bars"""
        # 重新订阅所有数据
        reqs: list = list(self.subscribed.values())
        for req in reqs:
            self.unsubscribe(req)
        self.subscribed.clear()
        for req in reqs:
            self.subscribe(req)
        self.gateway.write_log("完成所有行情重新订阅")

    def tickPrice(self, reqId: TickerId, tickType: TickType, price: float, attrib: TickAttrib) -> None:
        """
        tick价格更新回报。

        可能设置的字段:
        - bid_price_1 (TickTypeEnum.BID): 买一价
        - ask_price_1 (TickTypeEnum.ASK): 卖一价
        - last_price (TickTypeEnum.LAST): 最新成交价
        - high_price (TickTypeEnum.HIGH): 最高价
        - low_price (TickTypeEnum.LOW): 最低价
        - pre_close (TickTypeEnum.CLOSE): 昨收价
        - open_price (TickTypeEnum.OPEN): 开盘价
        """
        super().tickPrice(reqId, tickType, price, attrib)
        # self.gateway.write_log(f"reqId：{reqId}, tickType: {tickType}({TickTypeEnum.toStr(tickType)}), price: {price}, attrib: {attrib}")

        if tickType not in TICKFIELD_IB2VT:
            return

        tick: TickData = self.ticks.get(reqId)
        if not tick:
            self.gateway.write_log(f"收到未订阅的推送，reqId：{reqId}")
            return

        if price in (0, -1):
            # 设置跳过标记
            self.skip_next_ticksize[reqId] = True
            return
        
        name: str = TICKFIELD_IB2VT[tickType]
        setattr(tick, name, price)

        # 更新tick数据name字段
        contract: ContractData = self.contracts.get(tick.vt_symbol)
        if contract:
            tick.name = contract.name

        # 本地计算Forex of IDEALPRO和Spot Commodity的tick时间和最新价格
        if tick.exchange == Exchange.IDEALPRO or "CMDTY" in tick.symbol:
            if not tick.bid_price_1 or not tick.ask_price_1 or tick.low_price == -1:
                return
                
            # 计算中间价
            mid_price = (tick.bid_price_1 + tick.ask_price_1) / 2
            
            # 如果有contract.pricetick则进行round_to处理
            if contract and contract.pricetick:
                tick.last_price = round_to(mid_price, contract.pricetick)
            else:
                # 如果拿不到pricetick则直接使用中间价,并记录警告日志
                tick.last_price = mid_price
                self.gateway.write_log(f"Warning: {tick.vt_symbol} missing contract.pricetick, using raw mid price")

            # datetime是quote update time，不是last trade time，所以每次行情变化，都修改这个time
            tick.datetime = datetime.now(LOCAL_TZ)
        """
        Market data tick price callback. Handles all price related ticks. Every tickPrice callback is followed by a tickSize. 
        A tickPrice value of -1 or 0 followed by a tickSize of 0 indicates there is no data for this field currently available, whereas a tickPrice with a positive tickSize indicates an active quote of 0 (typically for a combo contract).
        """
        # IB API描述中，有tickPrice变化，一定会紧跟一个tickSize变化，所以，tickSize没更新之前，没必要提交on_tick，否则反而会给出错误的tickSize
        # Add data validation before sending tick
        # if not self.gateway.data_invalid(tick) and tick.last_price:
        #     self.gateway.on_tick(copy(tick))

        # 如果收到pre_close的tick，推送EVENT_PRE_CLOSE事件
        if tickType == TickTypeEnum.CLOSE:  # 9 corresponds to "pre_close" in TICKFIELD_IB2VT
            tick_copy = copy(tick)
            tick_copy.localtime = datetime.now(LOCAL_TZ)
            self.gateway.write_log(f"pre_close tick - Symbol: {tick_copy.symbol}, reqId：{reqId}, pre_close: {tick_copy.pre_close}")
            self.gateway.on_pre_close(tick_copy)

    def tickSize(self, reqId: TickerId, tickType: TickType, size: Decimal) -> None:
        """
        tick数量更新回报。

        可能设置的字段:
        - bid_volume_1 (TickTypeEnum.BID_SIZE): 买一量
        - ask_volume_1 (TickTypeEnum.ASK_SIZE): 卖一量
        - last_volume (TickTypeEnum.LAST_SIZE): 最新成交量
        - volume (TickTypeEnum.VOLUME): 总成交量
        - open_interest (TickTypeEnum.FUTURES_OPEN_INTEREST): 持仓量

        注意:
        - 当price为0或-1时会跳过对应的size更新
        """
        super().tickSize(reqId, tickType, size)
        # self.gateway.write_log(f"reqId：{reqId}, tickType: {tickType}({TickTypeEnum.toStr(tickType)}), size: {size}")

        # 检查是否需要跳过此次tickSize
        if self.skip_next_ticksize.get(reqId):
            self.skip_next_ticksize[reqId] = False
            return

        if tickType not in TICKFIELD_IB2VT:
            return

        tick: TickData = self.ticks.get(reqId)
        if not tick:
            self.gateway.write_log(f"收到未订阅的推送，reqId：{reqId}")
            return

        if tickType == TickTypeEnum.VOLUME and tick.exchange == Exchange.SMART:
            return

        name: str = TICKFIELD_IB2VT[tickType]
        setattr(tick, name, float(size))

        # datetime是quote update time，不是last trade time，所以每次行情变化，都修改这个time
        # tick.datetime = datetime.now(LOCAL_TZ)

        # Add data validation before sending tick
        if not self.gateway.data_invalid(tick) and tick.exchange != Exchange.SMART:
            # 美股不推送错位 量/价/datetime的on_tick，只更新订单簿等tick的字段
            self.gateway.on_tick(copy(tick))

    def tickString(self, reqId: TickerId, tickType: TickType, value: str) -> None:
        """
        tick字符串更新回报。

        可能设置的字段:
        1. 对于TickTypeEnum.LAST_TIMESTAMP:
        - datetime: 从秒级时间戳转换而来的UTC时间

        2. 对于TickTypeEnum.RT_TRD_VOLUME:
        格式: price;size;ms since epoch;total volume;VWAP;single_market_maker
        - last_price: 最新成交价
        - last_volume: 最新成交量
        - volume: 总成交量
        - datetime: 从毫秒级时间戳转换而来的本地时间
        """
        super().tickString(reqId, tickType, value)
        # self.gateway.write_log(f"reqId：{reqId}, tickType: {tickType}({TickTypeEnum.toStr(tickType)}), value: {value}")

        if tickType not in TICKFIELD_IB2VT:
            return

        tick: TickData = self.ticks.get(reqId)
        if not tick:
            self.gateway.write_log(f"收到未订阅的推送，reqId：{reqId}")
            return
        last_dt = tick.datetime
        
        # 处理RT_TRD_VOLUME(77)
        if tickType == TickTypeEnum.RT_TRD_VOLUME:
            # RT Volume格式: price;size;ms since epoch;total volume;VWAP;single_market_maker
            try:
                price_str, size_str, rt_time, volume, vwap, single_market_maker = value.split(";")
                
                # 更新成交量 - 使用total volume而不是单笔size
                if volume:
                    tick.volume = float(volume)*100

                if rt_time:
                    # 注意:rt_time是毫秒级时间戳
                    tick.datetime = datetime.fromtimestamp(int(rt_time) / 1000, LOCAL_TZ)
                
                # 更新最新价和成交量
                if price_str and size_str:
                    tick.last_price = float(price_str)
                    tick.last_volume = float(size_str)*100

            except ValueError:
                self.gateway.write_log(f"RT Volume数据格式错误: {value}")
                return

        # 处理LAST_TIMESTAMP(45)
        elif tickType == TickTypeEnum.LAST_TIMESTAMP:
            # 注意:LAST_TIMESTAMP是秒级时间戳
            tick.datetime = datetime.fromtimestamp(int(value), LOCAL_TZ)

        # 其他tickString类型暂不处理
        else:
            return

        # Add data validation before sending tick
        if not self.gateway.data_invalid(tick) and tick.datetime >= last_dt and tick.last_price:
            tick_copy = copy(tick)
            self.gateway.on_tick(tick_copy)
            
            # 为265598.SMART添加EVENT_TICK_RECORD事件推送
            if tick.vt_symbol == "265598.SMART" and tickType == TickTypeEnum.RT_TRD_VOLUME:
                tick_copy.localtime = datetime.now(LOCAL_TZ)
                self.gateway.on_tick_record(tick_copy)

    def tickGeneric(self, reqId: TickerId, tickType: TickType, value: float) -> None:
        """
        通用行情推送。

        可能设置的字段:
        - halted (TickTypeEnum.HALTED): 合约停牌状态
          值含义:
          -1: 停牌状态不可用(通常出现在冻结数据中)
          0: 正常交易(仅当合约在TWS观察列表中时返回)
          1: 一般性停牌(出于监管原因，可能包含/不包含波动性停牌)
          2: 波动性停牌(由交易所实施，用于防止极端波动)
        """
        super().tickGeneric(reqId, tickType, value)
        # self.gateway.write_log(f"reqId：{reqId}, tickType: {tickType}({TickTypeEnum.toStr(tickType)}), value: {value}")
        if tickType not in TICKFIELD_IB2VT:
            return

        tick: TickData = self.ticks.get(reqId)
        if not tick:
            self.gateway.write_log(f"收到未订阅的推送，reqId：{reqId}")
            return
        
        # 处理停牌状态HALTED(49)
        if tickType == TickTypeEnum.HALTED:
            '''
            The Halted tick type indicates if a contract has been halted for trading. It can have the following values:

            Value	Description
            -1	Halted status not available. Usually returned with frozen data.
            0	Not halted. This value will only be returned if the contract is in a TWS watchlist.
            1	General halt. Trading halt is imposed for purely regulatory reasons with/without volatility halt.
            2	Volatility halt. Trading halt is imposed by the exchange to protect against extreme volatility.
            '''
            # 检查是否为初始化或状态变更
            new_status = int(value)
            old_status = self.halted_status.get(tick.vt_symbol)
            
            # 只在初始化或状态变更时记录日志
            if old_status is None:
                # 初始化
                self.gateway.write_log(f"{tick.vt_symbol}交易状态初始化: {self.status_desc.get(new_status, '未知状态')}")
            elif old_status != new_status:
                # 状态变更
                self.gateway.write_log(f"{tick.vt_symbol}交易状态变更: {self.status_desc.get(old_status, '未知状态')} -> {self.status_desc.get(new_status, '未知状态')}")
            
            # 更新合约的停牌状态
            self.halted_status[tick.vt_symbol] = new_status

    def realtimeBar(self, reqId: TickerId, time: int, open_: float, high: float, low: float,
                    close: float, volume: Decimal, wap: Decimal, count: int) -> None:
        """5秒实时K线推送"""
        super().realtimeBar(reqId, time, open_, high, low, close, volume, wap, count)
        dt = datetime.fromtimestamp(time, LOCAL_TZ)
        # self.gateway.write_log(f'reqId: {reqId}, time: {dt}, open: {open_}, high: {high}, low: {low}, close: {close}, volume: {volume}, wap: {wap}, count: {count}')

        bar: BarData = self.realtime_bars.get(reqId)
        if not bar:
            self.gateway.write_log(f"收到未订阅的推送，reqId：{reqId}")
            return

        last_dt = bar.datetime
        bar.datetime = dt
        if last_dt and bar.datetime <= last_dt:
            return

        if not close:
            return

        bar.close_price = close
        bar.volume = float(volume)
        bar.open_price = open_
        bar.high_price = high
        bar.low_price = low

        if not self.gateway.data_invalid(bar):
            self.gateway.on_bar_mini(copy(bar))

    def tickOptionComputation(
        self,
        reqId: TickerId,
        tickType: TickType,
        tickAttrib: int,
        impliedVol: float,
        delta: float,
        optPrice: float,
        pvDividend: float,
        gamma: float,
        vega: float,
        theta: float,
        undPrice: float
    ):
        """
        期权计算结果推送。当期权或其标的物的市场发生变动时调用此方法。

        可能设置的字段:
        - bid (TickTypeEnum.BID_OPTION_COMPUTATION): 买价计算结果
        - ask (TickTypeEnum.ASK_OPTION_COMPUTATION): 卖价计算结果
        - last (TickTypeEnum.LAST_OPTION_COMPUTATION): 最新价计算结果
        - model (TickTypeEnum.MODEL_OPTION): 模型价计算结果

        参数说明:
        - reqId: 请求的唯一标识符
        - tickType: 期权计算类型
        - tickAttrib: 计算基准(0=收益率基准, 1=价格基准)
        - impliedVol: TWS期权模型计算的隐含波动率
        - delta: 期权delta值
        - optPrice: 期权价格
        - pvDividend: 期权标的预期股息的现值
        - gamma: 期权gamma值
        - vega: 期权vega值
        - theta: 期权theta值
        - undPrice: 标的物价格
        """
        super().tickOptionComputation(
            reqId,
            tickType,
            tickAttrib,
            impliedVol,
            delta,
            optPrice,
            pvDividend,
            gamma,
            vega,
            theta,
            undPrice,
        )
        # self.gateway.write_log(f"reqId：{reqId}, tickType: {tickType}({TickTypeEnum.toStr(tickType)}), "
        #                      f"impliedVol: {impliedVol}, delta: {delta}, optPrice: {optPrice}, "
        #                      f"pvDividend: {pvDividend}, gamma: {gamma}, vega: {vega}, theta: {theta}, undPrice: {undPrice}")

        tick: TickData = self.ticks.get(reqId)
        if not tick:
            self.gateway.write_log(f"收到未订阅的推送，reqId：{reqId}")
            return

        prefix: str = TICKFIELD_IB2VT[tickType]

        tick.extra["underlying_price"] = undPrice

        if optPrice:
            tick.extra[f"{prefix}_price"] = optPrice
            tick.extra[f"{prefix}_impv"] = impliedVol
            tick.extra[f"{prefix}_delta"] = delta
            tick.extra[f"{prefix}_gamma"] = gamma
            tick.extra[f"{prefix}_theta"] = theta
            tick.extra[f"{prefix}_vega"] = vega
        else:
            tick.extra[f"{prefix}_price"] = 0
            tick.extra[f"{prefix}_impv"] = 0
            tick.extra[f"{prefix}_delta"] = 0
            tick.extra[f"{prefix}_gamma"] = 0
            tick.extra[f"{prefix}_theta"] = 0
            tick.extra[f"{prefix}_vega"] = 0

    def tickSnapshotEnd(self, reqId: int) -> None:
        """行情切片查询返回完毕"""
        super().tickSnapshotEnd(reqId)

        tick: TickData = self.ticks.get(reqId)
        if not tick:
            self.gateway.write_log(f"收到未订阅的推送，reqId：{reqId}")
            return

        self.gateway.write_log(f"{tick.vt_symbol}行情切片查询成功")

    def orderStatus(
        self,
        orderId: OrderId,
        status: str,
        filled: Decimal,
        remaining: Decimal,
        avgFillPrice: float,
        permId: int,
        parentId: int,
        lastFillPrice: float,
        clientId: int,
        whyHeld: str,
        mktCapPrice: float,
    ) -> None:
        """订单状态更新回报"""
        super().orderStatus(
            orderId,
            status,
            filled,
            remaining,
            avgFillPrice,
            permId,
            parentId,
            lastFillPrice,
            clientId,
            whyHeld,
            mktCapPrice,
        )
        self.gateway.write_log(f"orderId：{orderId}, status：{status}, filled：{filled}, remaining：{remaining}, avgFillPrice：{avgFillPrice}, permId：{permId}, parentId：{parentId}, lastFillPrice：{lastFillPrice}, clientId：{clientId}, whyHeld：{whyHeld}, mktCapPrice：{mktCapPrice}")

        orderid: str = str(orderId)
        order: OrderData = self.orders.get(orderid)
        if not order:
            return

        order.traded = float(filled)

        # 过滤撤单中状态
        order_status: Status = STATUS_IB2VT.get(status)
        if order_status:
            order.status = order_status

        self.gateway.on_order(copy(order))

        # 记录委托状态日志
        self.gateway.write_log(f"委托状态更新: OrderId={orderId}, Status={status}, "
                             f"Filled={filled}, Remaining={remaining}, "
                             f"AvgFillPrice={avgFillPrice}, LastFillPrice={lastFillPrice}, "
                             f"{order}")

    def openOrder(
        self,
        orderId: OrderId,
        ib_contract: Contract,
        ib_order: Order,
        orderState: OrderState,
    ) -> None:
        """新订单回报"""
        super().openOrder(orderId, ib_contract, ib_order, orderState)
        self.gateway.write_log(f"orderId：{orderId}，orderState.status：{orderState.status}")

        orderid: str = str(orderId)

        if ib_order.orderRef:
            dt: datetime = datetime.strptime(ib_order.orderRef, "%Y-%m-%d %H:%M:%S")
        else:
            dt: datetime = datetime.now()

        # 优先使用本地缓存的委托记录，解决交易所传SMART时，返回数据的交易所可能发生变化的问题
        order: OrderData = self.orders.get(orderid)
        if not order:
            order: OrderData = OrderData(
                symbol=self.generate_symbol(ib_contract),
                exchange=EXCHANGE_IB2VT.get(ib_contract.exchange, Exchange.SMART),
                type=ORDERTYPE_IB2VT[ib_order.orderType],
                orderid=orderid,
                direction=DIRECTION_IB2VT[ib_order.action],
                volume=ib_order.totalQuantity,
                datetime=dt,
                gateway_name=self.gateway_name,
            )

        if order.type == OrderType.LIMIT:
            order.price = ib_order.lmtPrice
        elif order.type == OrderType.STOP:
            order.price = ib_order.auxPrice

        self.orders[orderid] = order
        # 没必要发送此事件，因为每次OnOrderStatus都会前，都会发送一次OnOpenOrder，而且OnOpenOrder的order中，status一直都是Submitted，会干扰策略的逻辑
        #self.gateway.on_order(copy(order))

    def updateAccountValue(self, key: str, val: str, currency: str, accountName: str) -> None:
        """账号更新回报"""
        super().updateAccountValue(key, val, currency, accountName)

        if not currency or key not in ACCOUNTFIELD_IB2VT:
            return

        accountid: str = f"{accountName}.{currency}"
        account: AccountData = self.accounts.get(accountid)
        if not account:
            account = AccountData(
                accountid=accountid,
                gateway_name=self.gateway_name
            )
            self.accounts[accountid] = account

        name: str = ACCOUNTFIELD_IB2VT[key]
        setattr(account, name, float(val))

    def updatePortfolio(
        self,
        contract: Contract,
        position: Decimal,
        marketPrice: float,
        marketValue: float,
        averageCost: float,
        unrealizedPNL: float,
        realizedPNL: float,
        accountName: str,
    ) -> None:
        """持仓更新回报"""
        super().updatePortfolio(
            contract,
            position,
            marketPrice,
            marketValue,
            averageCost,
            unrealizedPNL,
            realizedPNL,
            accountName,
        )

        # 美股股票持仓，需要转换交易所
        if contract.secType == "STK":
            if contract.exchange in US_STOCK_EXCHANGES or not contract.exchange and contract.primaryExchange in US_STOCK_EXCHANGES:
                exchange = Exchange.SMART
            else:
                if contract.exchange or contract.primaryExchange:
                    exchange: Exchange = EXCHANGE_IB2VT.get(contract.exchange or contract.primaryExchange)
                    if exchange:
                        self.gateway.write_log(f"STK exchange: {contract.exchange} or {contract.primaryExchange} not in US_STOCK_EXCHANGES, using {exchange.value} routing")
                else:
                    self.gateway.write_log(f"Warning: Unknown STK exchange for {contract.symbol}, using SMART routing")
                    exchange: Exchange = Exchange.SMART   # Use smart routing for default
        else:
            if contract.exchange or contract.primaryExchange:
                exchange: Exchange = EXCHANGE_IB2VT.get(contract.exchange or contract.primaryExchange)
            else:
                exchange: Exchange = Exchange.SMART   # Use smart routing for default
                self.gateway.write_log(f"Warning: Unknown exchange for {contract.secType} {contract.symbol}, using SMART routing")

        if not exchange:
            msg: str = f"存在不支持的交易所持仓：{self.generate_symbol(contract)} {contract.exchange} {contract.primaryExchange}"
            self.gateway.write_log(msg)
            return

        try:
            ib_size: float = float(contract.multiplier)
        except ValueError:
            ib_size = 1.
        price = averageCost / ib_size

        pos: PositionData = PositionData(
            symbol=self.generate_symbol(contract),
            exchange=exchange,
            direction=Direction.NET,
            volume=float(position),
            price=price,
            pnl=unrealizedPNL,
            gateway_name=self.gateway_name,
        )
        self.gateway.on_position(pos)

    def updateAccountTime(self, timeStamp: str) -> None:
        """账号更新时间回报"""
        super().updateAccountTime(timeStamp)
        for account in self.accounts.values():
            self.gateway.on_account(copy(account))

    @iswrapper
    def position(self, account: str, contract: Contract, position: Decimal, avgCost: float) -> None:
        """持仓数据回报"""
        super().position(account, contract, position, avgCost)
        
        # 美股股票持仓，需要转换交易所
        if contract.secType == "STK":
            if contract.exchange in US_STOCK_EXCHANGES or not contract.exchange and contract.primaryExchange in US_STOCK_EXCHANGES:
                exchange = Exchange.SMART
            else:
                if contract.exchange or contract.primaryExchange:
                    exchange: Exchange = EXCHANGE_IB2VT.get(contract.exchange or contract.primaryExchange)
                    if exchange:
                        self.gateway.write_log(f"STK exchange: {contract.exchange} or {contract.primaryExchange} not in US_STOCK_EXCHANGES, using {exchange.value} routing")
                else:
                    self.gateway.write_log(f"Warning: Unknown STK exchange for {contract.symbol}, using SMART routing")
                    exchange: Exchange = Exchange.SMART   # Use smart routing for default
        else:
            if contract.exchange or contract.primaryExchange:
                exchange: Exchange = EXCHANGE_IB2VT.get(contract.exchange or contract.primaryExchange)
            else:
                exchange: Exchange = Exchange.SMART   # Use smart routing for default
                self.gateway.write_log(f"Warning: Unknown exchange for {contract.secType} {contract.symbol}, using SMART routing")

        if not exchange:
            msg: str = f"存在不支持的交易所持仓：{self.generate_symbol(contract)} {contract.exchange} {contract.primaryExchange}"
            self.gateway.write_log(msg)
            return

        try:
            ib_size: float = float(contract.multiplier)
        except (ValueError, TypeError):
            ib_size = 1.
        
        # 计算平均成本价格
        if ib_size != 0:
            price = avgCost / ib_size
        else:
            price = avgCost

        pos: PositionData = PositionData(
            symbol=self.generate_symbol(contract),
            exchange=exchange,
            direction=Direction.NET,
            volume=float(position),
            price=price,
            pnl=0.0,  # 这里没有未实现盈亏信息，设为0
            gateway_name=self.gateway_name,
        )
        self.gateway.on_position(pos)

    @iswrapper
    def positionEnd(self) -> None:
        """持仓数据推送结束"""
        super().positionEnd()
        self.gateway.write_log("持仓信息查询完成")

    def contractDetails(self, reqId: int, contractDetails: ContractDetails) -> None:
        """合约数据更新回报"""
        super().contractDetails(reqId, contractDetails)

        # 提取合约信息
        ib_contract: Contract = contractDetails.contract

        # 处理合约乘数为0的情况
        if not ib_contract.multiplier:
            ib_contract.multiplier = 1.

        # 字符串风格的代码，需要从缓存中获取
        if reqId in self.reqid_symbol_map:
            symbol: str = self.reqid_symbol_map[reqId]
        # 否则默认使用数字风格代码
        else:
            symbol: str = str(ib_contract.conId)

        # 过滤不支持的类型
        product: Product = PRODUCT_IB2VT.get(ib_contract.secType)
        if not product:
            return

        # 生成合约
        contract: ContractData = ContractData(
            symbol=symbol,
            exchange=EXCHANGE_IB2VT[ib_contract.exchange],
            name=contractDetails.longName,
            product=PRODUCT_IB2VT[ib_contract.secType],
            size=float(ib_contract.multiplier),
            pricetick=contractDetails.minTick,
            min_volume=contractDetails.minSize,
            net_position=True,
            history_data=True,
            stop_supported=True,
            gateway_name=self.gateway_name,
        )

        if contract.product == Product.OPTION:
            underlying_symbol: str = str(contractDetails.underConId)

            contract.option_portfolio = underlying_symbol + "_O"
            contract.option_type = OPTION_IB2VT.get(ib_contract.right)
            contract.option_strike = ib_contract.strike
            contract.option_index = str(ib_contract.strike)
            contract.option_expiry = datetime.strptime(ib_contract.lastTradeDateOrContractMonth, "%Y%m%d")
            contract.option_underlying = underlying_symbol + "_" + ib_contract.lastTradeDateOrContractMonth

        self.gateway.on_contract(contract)

        self.contracts[contract.vt_symbol] = contract
        self.ib_contracts[contract.vt_symbol] = ib_contract
        self.contracts_details[contract.vt_symbol] = contractDetails

        # Add trading hours update
        self.gateway.update_trading_hours(contract.vt_symbol, contractDetails)

    def contractDetailsEnd(self, reqId: int) -> None:
        """合约数据更新结束回报"""
        super().contractDetailsEnd(reqId)

        # 只需要对期权查询做处理
        underlying: Contract = self.reqid_underlying_map.get(reqId)
        if not underlying:
            return

        # 输出日志信息
        symbol: str = self.generate_symbol(underlying)
        exchange: Exchange = EXCHANGE_IB2VT.get(underlying.exchange, Exchange.SMART)
        vt_symbol: str = f"{symbol}.{exchange.value}"

        self.gateway.write_log(f"{vt_symbol}期权链查询成功")

        # 保存期权合约到文件
        self.save_contract_data()

    def execDetails(self, reqId: int, contract: Contract, execution: Execution) -> None:
        """交易数据更新回报"""
        super().execDetails(reqId, contract, execution)
        # 记录成交信息日志
        self.gateway.write_log(f"收到成交信息: OrderId={execution.orderId}, "
                             f"ExecId={execution.execId}, Time={execution.time}, "
                             f"Side={execution.side}, Shares={execution.shares}, "
                             f"Price={execution.price}")

        # 解析成交时间
        time_str: str = execution.time
        time_split: list = time_str.split(" ")
        words_count: int = 3

        if len(time_split) == words_count:
            timezone = time_split[-1]
            time_str = time_str.replace(f" {timezone}", "")
            tz = ZoneInfo(timezone)
        elif len(time_split) == (words_count - 1):
            tz = LOCAL_TZ
        else:
            self.gateway.write_log(f"收到不支持的时间格式：{time_str}")
            return

        dt: datetime = datetime.strptime(time_str, "%Y%m%d %H:%M:%S")
        dt: datetime = dt.replace(tzinfo=tz)

        if tz != LOCAL_TZ:
            dt = dt.astimezone(LOCAL_TZ)

        # 优先使用本地缓存的委托记录，解决交易所传SMART时，返回数据的交易所可能发生变化的问题
        orderid: str = str(execution.orderId)
        order: OrderData = self.orders.get(orderid)

        if order:
            symbol: str = order.symbol
            exchange: Exchange = order.exchange
        else:
            symbol: str = self.generate_symbol(contract)
            exchange: Exchange = EXCHANGE_IB2VT.get(contract.exchange, Exchange.SMART)

        # 推送成交数据
        trade: TradeData = TradeData(
            symbol=symbol,
            exchange=exchange,
            orderid=orderid,
            tradeid=str(execution.execId),
            direction=DIRECTION_IB2VT[execution.side],
            price=execution.price,
            volume=float(execution.shares),
            datetime=dt,
            gateway_name=self.gateway_name,
        )

        self.gateway.on_trade(trade)

    def managedAccounts(self, accountsList: str) -> None:
        """所有子账户回报"""
        super().managedAccounts(accountsList)

        if not self.account:
            for account_code in accountsList.split(","):
                if account_code:
                    self.account = account_code

        self.gateway.write_log(f"当前使用的交易账号为{self.account}")
        self.client.reqAccountUpdates(True, self.account)

    def historicalData(self, reqId: int, ib_bar: IbBarData) -> None:
        """历史数据更新回报"""
        # 日级别数据和周级别日期数据的数据形式为%Y%m%d
        time_str: str = ib_bar.date
        time_split: list = time_str.split(" ")
        words_count: int = 3

        if ":" not in time_str:
            words_count -= 1

        if len(time_split) == words_count:
            timezone = time_split[-1]
            time_str = time_str.replace(f" {timezone}", "")
            tz = ZoneInfo(timezone)
        elif len(time_split) == (words_count - 1):
            tz = LOCAL_TZ
        else:
            self.gateway.write_log(f"收到不支持的时间格式：{time_str}")
            return

        if ":" in time_str:
            dt: datetime = datetime.strptime(time_str, "%Y%m%d %H:%M:%S")
        else:
            dt: datetime = datetime.strptime(time_str, "%Y%m%d")
        dt: datetime = dt.replace(tzinfo=tz)

        if tz != LOCAL_TZ:
            dt: datetime = dt.astimezone(LOCAL_TZ)

        bar: BarData = BarData(
            symbol=self.history_req.symbol,
            exchange=self.history_req.exchange,
            datetime=dt,
            interval=self.history_req.interval,
            volume=float(ib_bar.volume),
            open_price=ib_bar.open,
            high_price=ib_bar.high,
            low_price=ib_bar.low,
            close_price=ib_bar.close,
            gateway_name=self.gateway_name
        )
        if bar.volume < 0:
            bar.volume = 0

        self.history_buf.append(bar)

    def historicalDataEnd(self, reqId: int, start: str, end: str) -> None:
        """历史数据查询完毕回报"""
        self.history_condition.acquire()
        self.history_condition.notify()
        self.history_condition.release()

    def connect(self, host: str, port: int, clientid: int, account: str) -> None:
        """连接TWS"""
        if self.status:
            return

        self.host = host
        self.port = port
        self.clientid = clientid
        self.account = account

        self.client.connect(host, port, clientid)
        self.thread = Thread(target=self.client.run)
        self.thread.start()

    def check_connection(self) -> None:
        """检查连接"""
        if self.client.isConnected():
            return

        if self.status:
            self.close()

        self.client.connect(self.host, self.port, self.clientid)

        self.thread = Thread(target=self.client.run)
        self.thread.start()

    def close(self) -> None:
        """断开TWS连接"""
        if not self.status:
            return

        self.save_contract_data()

        self.status = False
        self.client.disconnect()

    def query_option_portfolio(self, underlying: Contract) -> None:
        """查询期权链合约数据"""
        if not self.status:
            return

        # 解析IB期权合约
        ib_contract: Contract = Contract()
        ib_contract.symbol = underlying.symbol
        ib_contract.currency = underlying.currency

        # 期货期权必须使用指定交易所
        if underlying.secType == "FUT":
            ib_contract.secType = "FOP"
            ib_contract.exchange = underlying.exchange
        # 现货期权支持智能路由
        else:
            ib_contract.secType = "OPT"
            ib_contract.exchange = "SMART"

        # 通过TWS查询合约信息
        self.reqid += 1
        self.client.reqContractDetails(self.reqid, ib_contract)

        # 缓存查询记录
        self.reqid_underlying_map[self.reqid] = underlying

    def query_contract(self, symbol: str, exchange: Exchange) -> Optional[Contract]:
        """查询合约信息，返回IB合约对象"""
        if not self.status:
            return

        if not self.data_ready:
            return

        if exchange not in EXCHANGE_VT2IB:
            self.gateway.write_log(f"不支持的交易所{exchange}")
            return

        if " " in symbol and JOIN_SYMBOL not in symbol:
            self.gateway.write_log("订阅失败，合约代码中包含空格")
            return
        
        # 解析IB合约详情
        ib_contract: Contract = generate_ib_contract(symbol, exchange)
        if not ib_contract:
            self.gateway.write_log("代码解析失败，请检查格式是否正确")
            return None

        # 通过TWS查询合约信息
        self.reqid += 1
        contract_reqid = self.reqid
        self.client.reqContractDetails(contract_reqid, ib_contract)

        # 如果使用了字符串风的代码，则需要缓存
        if JOIN_SYMBOL in symbol:
            self.reqid_symbol_map[contract_reqid] = symbol

        return ib_contract

    def subscribe(self, req: SubscribeRequest) -> None:
        """订阅行情"""
        # 过滤重复订阅
        if req.vt_symbol in self.subscribed:
            return
        
        self.subscribed[req.vt_symbol] = req

        # 通过TWS查询合约信息
        ib_contract: Contract = self.query_contract(req.symbol, req.exchange)
        if not ib_contract:
            self.gateway.write_log(f"合约{req.vt_symbol}查询失败，无法订阅行情")
            return

        #  订阅tick数据并创建tick对象缓冲区
        self.reqid += 1
        tick_reqid = self.reqid
        # 375->77: RT Trade Volume; 233->48: RT Volume (Time & Sales)
        self.client.reqMktData(tick_reqid, ib_contract, "375", False, False, [])
        self.gateway.write_log(f"订阅{req.vt_symbol}行情，reqId: {tick_reqid}")

        tick: TickData = TickData(
            symbol=req.symbol,
            exchange=req.exchange,
            datetime=datetime.now(LOCAL_TZ),
            gateway_name=self.gateway_name
        )
        tick.extra = {}

        self.ticks[tick_reqid] = tick

        # 订阅5秒K线
        if self.gateway.use_5s_bar:
            self.reqid += 1
            bar_reqid = self.reqid
            self.client.reqRealTimeBars(
                bar_reqid,
                ib_contract,
                5,  # 5秒bar
                "TRADES" if tick.exchange != Exchange.IDEALPRO else "MIDPOINT",  # 使用中间价
                True,  # 不使用RTH
                []  # 空选项列表
            )
            self.gateway.write_log(f"订阅{req.vt_symbol} 5秒K线，reqId: {bar_reqid}")

            self.realtime_bars[bar_reqid] = BarData(
                symbol=req.symbol,
                exchange=req.exchange,
                interval=Interval.SECOND_5,
                datetime=datetime.now(LOCAL_TZ),
                gateway_name=self.gateway_name
            )

    def send_order(self, req: OrderRequest) -> str:
        """委托下单"""
        if not self.status:
            return ""
        if not self.order_ready:
            self.gateway.write_log(f"API还没有完全初始化完毕,还没有收到nextValidID,暂不能下单。symbol:{req.vt_symbol},direction:{req.direction},price:{req.price},volume:{req.volume}")
            return ""

        if req.exchange not in EXCHANGE_VT2IB:
            self.gateway.write_log(f"不支持的交易所：{req.exchange}")
            return ""

        if req.type not in ORDERTYPE_VT2IB:
            self.gateway.write_log(f"不支持的价格类型：{req.type}")
            return ""

        if " " in req.symbol and JOIN_SYMBOL not in req.symbol:
            self.gateway.write_log("委托失败，合约代码中包含空格")
            return ""

        self.orderid += 1

        ib_contract: Contract = generate_ib_contract(req.symbol, req.exchange)
        if not ib_contract:
            return ""

        ib_order: Order = Order()
        ib_order.orderId = self.orderid
        ib_order.clientId = self.clientid
        ib_order.action = DIRECTION_VT2IB[req.direction]
        ib_order.orderType = ORDERTYPE_VT2IB[req.type]
        ib_order.totalQuantity = Decimal(format_float(req.volume))
        ib_order.account = self.account
        ib_order.orderRef = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 非常规交易时间
        # ib_order.outsideRth = True

        if req.type == OrderType.LIMIT:
            ib_order.lmtPrice = float(format_float(req.price))
        elif req.type == OrderType.STOP:
            ib_order.auxPrice = float(format_float(req.price))

        # 使用Adaptive算法
        # AvailableAlgoParams.FillAdaptiveParams(ib_order)

        # 使用PctVol算法，动态获取endTime
        '''
        vt_symbol = req.vt_symbol
        trading_info = self.gateway.tradinghours_info.get(vt_symbol, None)
        if not trading_info:
            return ""

        # 获取时区
        time_zone_id = trading_info.get('timeZoneId', 'US/Eastern')
        time_zone = ZoneInfo(time_zone_id)
        # 当前时间转到合约时区
        start_time_dt = datetime.now(LOCAL_TZ)
        now_tz = start_time_dt.astimezone(time_zone)
        now_str = now_tz.strftime("%Y%m%d:%H%M%S")
        # 遍历tradingPeriod，找到当前时间属于哪一组
        found_period = None
        for start, end in trading_info.get('tradingPeriod', []):
            if start <= now_str < end:
                found_period = (start, end)
                break
        if not found_period:
            return ""
        start, end = found_period
        # 解析end的日期和时间
        end_date = end[:8]
        end_time = end[9:]
        now_date = now_str[:8]
        # 如果end_time不是当天，则endTime用23:59:59
        if end_date != now_date:
            end_time_fmt = "23:59:59"
        else:
            end_time_fmt = f"{end_time[:2]}:{end_time[2:4]}:{end_time[4:6]}"
        # 构造endTime字符串，带时区
        end_time_str = f"{end_time_fmt} {time_zone_id}"
        start_time_str = f"{now_tz.strftime('%H:%M:%S')} {time_zone_id}"

        AvailableAlgoParams.FillPctVolParams(
            ib_order,
            pctVol=0.1,
            startTime=start_time_str,
            endTime=end_time_str,
            noTakeLiq=False
        )
        '''
        ib_order.algoStrategy = "PctVol"
        ib_order.algoParams = []
        ib_order.algoParams.append(TagValue("pctVol", 0.2))
        # ib_order.algoParams.append(TagValue("startTime", startTime)) # test
        # ib_order.algoParams.append(TagValue("endTime", "14:00:00 US/Eastern")) # 不传入默认为收盘时间
        ib_order.algoParams.append(TagValue("noTakeLiq", int(False)))

        # ib_order.algoStrategy = "Twap"
        # ib_order.algoParams = []
        # ib_order.algoParams.append(TagValue("allowPastEndTime", int(True)))

        # 移除不被支持的属性
        if hasattr(ib_order, 'etradeOnly'):
            del ib_order.etradeOnly

        self.client.placeOrder(self.orderid, ib_contract, ib_order)
        self.client.reqIds(1)

        order: OrderData = req.create_order_data(str(self.orderid), self.gateway_name)
        self.orders[order.orderid] = order
        order.datetime = datetime.now(LOCAL_TZ)
        self.gateway.on_order(order)
        
        # 记录发单日志
        self.gateway.write_log(f"OrderId={self.orderid}, "
                             f"Symbol={req.symbol}, Direction={req.direction}, "
                             f"Volume={req.volume}"
                             f"发送订单：algoStrategy={getattr(ib_order, 'algoStrategy', '')}, "
                             f"algoParams={getattr(ib_order, 'algoParams', [])}, ")
        
        return order.vt_orderid

    def cancel_order(self, req: CancelRequest) -> None:
        """委托撤单"""
        if not self.status:
            return
        if not self.order_ready:
            self.gateway.write_log(f"API还没有完全初始化完毕,还没有收到nextValidID,暂不能撤单。oderid:{req.orderid},symbol:{req.vt_symbol}")
            return

        cancel: OrderCancel = OrderCancel()
        self.client.cancelOrder(int(req.orderid), cancel)
        # IB API 10.9.1在撤单时，新增1个参数，撤单时间
        # manualCancelOrderTime:str = datetime.now(LOCAL_TZ).strftime("%Y%m%d-%H:%M:%S")
        # self.client.cancelOrder(int(req.orderid), manualCancelOrderTime)

    def cancel_all(self) -> None:
        """取消所有未结订单"""
        self.client.reqGlobalCancel()
        self.gateway.write_log("已发送请求，取消所有未结订单")

    def query_position(self) -> None:
        """查询持仓"""
        if not self.status:
            return
        
        self.client.reqPositions()
        self.gateway.write_log("开始查询持仓信息")

    def query_history(self, req: HistoryRequest) -> list[BarData]:
        """查询历史数据"""
        contract: ContractData = self.contracts[req.vt_symbol]
        if not contract:
            self.gateway.write_log(f"找不到合约：{req.vt_symbol}，请先订阅")
            return []

        self.history_req = req

        self.reqid += 1

        ib_contract: Contract = generate_ib_contract(req.symbol, req.exchange)

        if req.end:
            end: datetime = req.end
        else:
            end: datetime = datetime.now(LOCAL_TZ)

        # 使用UTC结束时间戳
        utc_end: datetime = end.astimezone(UTC_TZ)
        end_str: str = utc_end.strftime("%Y%m%d-%H:%M:%S")

        delta: timedelta = end - req.start
        days: float = delta.total_seconds() / 86400
        if days < 365:
            duration: str = f"{max(1, math.ceil(days))} D"
        else:
            duration: str = f"{math.ceil(days/365)} Y"

        bar_size: str = INTERVAL_VT2IB[req.interval]

        if contract.product in [Product.SPOT, Product.FOREX]:
            bar_type: str = "MIDPOINT"
        else:
            bar_type: str = "TRADES"

        self.history_reqid = self.reqid
        self.client.reqHistoricalData(
            self.reqid,
            ib_contract,
            end_str,
            duration,
            bar_size,
            bar_type,
            0,
            1,
            False,
            []
        )

        self.history_condition.acquire()    # 等待异步数据返回
        self.history_condition.wait(600)
        self.history_condition.release()

        history: list[BarData] = self.history_buf
        self.history_buf: list[BarData] = []       # 创新新的缓冲列表
        self.history_req: HistoryRequest = None

        return history

    def load_contract_data(self) -> None:
        """加载本地合约数据"""
        f = shelve.open(self.data_filepath)
        contracts = f.get("contracts", {})
        ib_contracts = f.get("ib_contracts", {})
        tradinghours_info = f.get("tradinghours_info", {})
        f.close()

        self.contracts.update(contracts)
        self.ib_contracts.update(ib_contracts)
        self.gateway.tradinghours_info.update(tradinghours_info)

        # 推送合约数据
        for contract in self.contracts.values():
            self.gateway.on_contract(contract)

        self.gateway.write_log("本地缓存合约信息加载成功")

    def save_contract_data(self) -> None:
        """保存合约数据至本地"""
        # 保存前确保所有合约数据接口名称为IB，避免其他模块的处理影响
        contracts: dict[str, ContractData] = {}
        for vt_symbol, contract in self.contracts.items():
            c: ContractData = copy(contract)
            c.gateway_name = self.gateway.default_name
            contracts[vt_symbol] = c

        f = shelve.open(self.data_filepath)
        f["contracts"] = contracts
        f["ib_contracts"] = self.ib_contracts
        f["tradinghours_info"] = self.gateway.tradinghours_info
        f.close()

    def generate_symbol(self, ib_contract: Contract) -> str:
        """生成合约代码"""
        # 生成字符串风格代码
        fields: list = [ib_contract.symbol]

        if ib_contract.secType in ["FUT", "OPT", "FOP"]:
            fields.append(ib_contract.lastTradeDateOrContractMonth)

        if ib_contract.secType in ["OPT", "FOP"]:
            fields.append(ib_contract.right)
            fields.append(str(ib_contract.strike))
            fields.append(str(ib_contract.multiplier))

        fields.append(ib_contract.currency)
        fields.append(ib_contract.secType)

        symbol: str = JOIN_SYMBOL.join(fields)
        exchange: Exchange = EXCHANGE_IB2VT.get(ib_contract.exchange, Exchange.SMART)
        vt_symbol: str = f"{symbol}.{exchange.value}"

        # 在合约信息中找不到字符串风格代码，则使用数字代码
        if vt_symbol not in self.contracts:
            symbol = str(ib_contract.conId)

        return symbol

    def query_tick(self, vt_symbol: str) -> None:
        """查询行情切片"""
        if not self.status:
            return

        contract: ContractData = self.contracts.get(vt_symbol)
        if not contract:
            self.gateway.write_log(f"查询行情切片失败，找不到{vt_symbol}对应的合约数据")
            return

        ib_contract: Contract = self.ib_contracts.get(vt_symbol)
        if not ib_contract:
            self.gateway.write_log(f"查询行情切片失败，找不到{vt_symbol}对应的IB合约数据")
            return

        self.reqid += 1
        self.client.reqMktData(self.reqid, ib_contract, "", True, False, [])

        tick: TickData = TickData(
            symbol=contract.symbol,
            exchange=contract.exchange,
            datetime=datetime.now(LOCAL_TZ),
            gateway_name=self.gateway_name
        )
        tick.extra = {}

        self.ticks[self.reqid] = tick

    def query_position(self) -> None:
        """查询持仓"""
        if not self.status:
            return
        
        self.client.reqPositions()
        self.gateway.write_log("开始查询持仓信息")

    def unsubscribe(self, req: SubscribeRequest) -> None:
        """退订tick数据更新"""
        # 移除订阅记录
        if req.vt_symbol not in self.subscribed:
            return
        self.subscribed.pop(req.vt_symbol)

        # 获取并退订所有相关的tick reqId
        tick_reqids = []
        for reqid, tick in list(self.ticks.items()):
            if tick.vt_symbol == req.vt_symbol:
                tick_reqids.append(reqid)
                
        for reqid in tick_reqids:
            self.client.cancelMktData(reqid)
            self.gateway.write_log(f"退订{req.vt_symbol}行情，reqId: {reqid}")
            self.ticks.pop(reqid)
    
        # 获取并退订所有相关的5秒K线 reqId
        bar_reqids = []
        for reqid, bar in list(self.realtime_bars.items()):
            if bar.vt_symbol == req.vt_symbol:
                bar_reqids.append(reqid)
                
        for reqid in bar_reqids:
            self.client.cancelRealTimeBars(reqid)
            self.gateway.write_log(f"退订{req.vt_symbol} 5秒K线，reqId: {reqid}")
            self.realtime_bars.pop(reqid)


def format_float(f: float) -> str:
    """
    Convert float number to string with correct precision.
    
    Fix potential IB error: Unable to parse field 'Order Size' for input string with excessive precision
    """
    return format_float_positional(f, trim='-')


def generate_ib_contract(symbol: str, exchange: Exchange) -> Optional[Contract]:
    """生产IB合约"""
    # 字符串代码
    if JOIN_SYMBOL in symbol:
        try:
            fields: list = symbol.split(JOIN_SYMBOL)

            ib_contract: Contract = Contract()
            ib_contract.exchange = EXCHANGE_VT2IB[exchange]
            ib_contract.secType = fields[-1]
            ib_contract.currency = fields[-2]
            ib_contract.symbol = fields[0]

            if ib_contract.secType in ["FUT", "OPT", "FOP"]:
                # ib_contract.lastTradeDateOrContractMonth = fields[1]
                if len(fields) > 3:
                    ib_contract.lastTradeDateOrContractMonth = fields[1]

            if ib_contract.secType == "FUT":
                if len(fields) == 5:
                    ib_contract.multiplier = int(fields[2])

            if ib_contract.secType in ["OPT", "FOP"]:
                ib_contract.right = fields[2]
                ib_contract.strike = float(fields[3])
                ib_contract.multiplier = int(fields[4])
        except IndexError:
            ib_contract = None
    # 数字代码（ConId）
    else:
        if symbol.isdigit():
            ib_contract: Contract = Contract()
            ib_contract.exchange = EXCHANGE_VT2IB[exchange]
            ib_contract.conId = symbol
        else:
            ib_contract = None

    return ib_contract
