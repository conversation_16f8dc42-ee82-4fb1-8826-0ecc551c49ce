"""
稳定ID与group_id映射表维护脚本

功能：
1. 从美股品种信息.xlsx读取稳定ID（stable_id）和conid
2. 通过conid在IbProduct表中查找对应的group_id
3. 检查美股品种核查.xlsx中的稳定ID，判断是否在重点核查列表中
4. 维护stable_id与group_id的映射关系表
"""

import sys
import pandas as pd
from typing import Dict, Set, List
import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志

from collections import defaultdict

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from utils.database_manager import db_manager, StableIdGroupMapping, IbProduct


class StableIdGroupMappingManager:
    """稳定ID与group_id映射表管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.stock_info_file = "美股品种信息.xlsx"
        self.focus_check_file = "美股重点核查.xlsx"
        self.ib_product_cache = None  # IbProduct表缓存
        
    def read_stock_info_excel(self) -> pd.DataFrame:
        """读取美股品种信息Excel文件
        
        Returns:
            pd.DataFrame: 包含stable_id和conid的DataFrame
        """
        try:
            if not os.path.exists(self.stock_info_file):
                logger.error(f"文件不存在: {self.stock_info_file}")
                return pd.DataFrame()
                
            # 读取Excel文件的"稳定ID" sheet
            df = pd.read_excel(self.stock_info_file, sheet_name="稳定ID")
            logger.info(f"成功读取美股品种信息稳定ID sheet，共{len(df)}条记录")
            
            # 检查必要的列是否存在
            required_columns = ['ID', 'conid']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"Excel文件稳定ID sheet缺少必要的列: {missing_columns}")
                return pd.DataFrame()
                
            # 过滤掉空值
            df = df.dropna(subset=required_columns)
            logger.info(f"过滤空值后，剩余{len(df)}条有效记录")
            
            # 重命名列以保持后续代码兼容性
            df = df[required_columns].rename(columns={'ID': '稳定ID'})
            
            return df
            
        except Exception as e:
            logger.error(f"读取美股品种信息Excel文件时发生错误: {str(e)}")
            return pd.DataFrame()
    
    def read_focus_check_excel(self) -> Set[int]:
        """读取美股重点核查Excel文件
        
        Returns:
            Set[int]: 重点核查的稳定ID集合
        """
        try:
            if not os.path.exists(self.focus_check_file):
                logger.warning(f"重点核查文件不存在: {self.focus_check_file}，将忽略重点核查标记")
                return set()
                
            # 读取Excel文件
            df = pd.read_excel(self.focus_check_file)
            logger.info(f"成功读取重点核查信息，共{len(df)}条记录")
            
            # 检查稳定ID列是否存在
            if '稳定ID' not in df.columns:
                logger.error(f"重点核查Excel文件缺少'稳定ID'列")
                return set()
                
            # 过滤掉空值并转换为集合
            stable_ids = set(df['稳定ID'].dropna().astype(int))
            logger.info(f"重点核查包含{len(stable_ids)}个不同的稳定ID")
            
            return stable_ids
            
        except Exception as e:
            logger.error(f"读取重点核查Excel文件时发生错误: {str(e)}")
            return set()
    
    def load_ib_product_cache(self):
        """一次性加载IbProduct表所有数据到缓存"""
        try:
            logger.info("开始加载IbProduct表数据到缓存...")
            
            # 查询所有IbProduct数据
            query = IbProduct.select(IbProduct.conid, IbProduct.group_id).where(
                IbProduct.group_id.is_null(False)
            )
            
            # 构建缓存字典
            conid_groups = defaultdict(set)
            total_records = 0
            
            for record in query:
                conid_groups[record.conid].add(record.group_id)
                total_records += 1
            
            logger.info(f"加载了{total_records}条IbProduct记录，涉及{len(conid_groups)}个不同的conid")
            
            # 检查conid对应多个group_id的情况
            conid_to_group = {}
            errors = []
            
            for conid, group_ids in conid_groups.items():
                if len(group_ids) == 1:
                    conid_to_group[conid] = list(group_ids)[0]
                else:
                    errors.append(f"conid {conid} 对应多个group_id: {group_ids}")
            
            if errors:
                logger.error("发现以下conid对应多个group_id的错误:")
                for error in errors[:10]:  # 只显示前10个错误
                    logger.error(f"  {error}")
                if len(errors) > 10:
                    logger.error(f"  ... 还有{len(errors) - 10}个类似错误")
                raise ValueError("存在conid对应多个group_id的情况")
            
            self.ib_product_cache = conid_to_group
            logger.info(f"IbProduct缓存构建完成，包含{len(conid_to_group)}个有效的conid->group_id映射")
            
        except Exception as e:
            logger.error(f"加载IbProduct表缓存时发生错误: {str(e)}")
            raise
    
    def get_group_id_from_cache(self, conid: int) -> int:
        """从缓存中获取conid对应的group_id
        
        Args:
            conid: 合约ID
            
        Returns:
            int: 对应的group_id，如果不存在则返回None
        """
        if self.ib_product_cache is None:
            raise ValueError("IbProduct缓存未初始化，请先调用load_ib_product_cache()")
            
        return self.ib_product_cache.get(conid)
    
    def clear_mapping_table(self):
        """清空映射表"""
        try:
            with db_manager.common_db.atomic():
                deleted_count = StableIdGroupMapping.delete().execute()
                logger.info(f"已清空映射表，删除了{deleted_count}条记录")
                
        except Exception as e:
            logger.error(f"清空映射表时发生错误: {str(e)}")
            raise
    
    def build_mapping_data(self, stock_info_df: pd.DataFrame, 
                          focus_stable_ids: Set[int]) -> List[Dict]:
        """构建映射表数据
        
        Args:
            stock_info_df: 股票信息DataFrame
            focus_stable_ids: 重点核查的稳定ID集合
            
        Returns:
            List[Dict]: 映射表数据列表
        """
        if self.ib_product_cache is None:
            raise ValueError("IbProduct缓存未初始化，请先调用load_ib_product_cache()")
        
        mapping_data = []
        missing_group_ids = []
        
        for _, row in stock_info_df.iterrows():
            stable_id = int(row['稳定ID'])
            conid = int(row['conid'])
            
            # 从缓存中查找对应的group_id
            group_id = self.get_group_id_from_cache(conid)
            if group_id is None:
                missing_group_ids.append((stable_id, conid))
                continue
                
            # 判断是否在重点核查中
            is_priority = stable_id in focus_stable_ids
            
            mapping_data.append({
                'stable_id': stable_id,
                'group_id': group_id,
                'priority': is_priority
            })
        
        if missing_group_ids:
            logger.warning(f"以下{len(missing_group_ids)}个记录未找到对应的group_id：")
            for stable_id, conid in missing_group_ids[:10]:  # 只显示前10个
                logger.warning(f"  stable_id: {stable_id}, conid: {conid}")
            if len(missing_group_ids) > 10:
                logger.warning(f"  ... 还有{len(missing_group_ids) - 10}个类似记录")
        
        logger.info(f"构建了{len(mapping_data)}条映射数据")
        return mapping_data
    
    def save_mapping_data(self, mapping_data: List[Dict]):
        """保存映射数据到数据库
        
        Args:
            mapping_data: 映射数据列表
        """
        try:
            if not mapping_data:
                logger.warning("没有映射数据需要保存")
                return
                
            # 批量保存数据
            db_manager.batch_save_to_db(
                mapping_data, 
                StableIdGroupMapping, 
                primary_key="stable_id",
                # replace=True  # 使用替换模式
            )
            
            logger.info(f"成功保存{len(mapping_data)}条映射数据到数据库")
            
        except Exception as e:
            logger.error(f"保存映射数据时发生错误: {str(e)}")
            raise
    
    def update_stable_id_group_mapping(self):
        """更新稳定ID与group_id映射表的主函数"""
        try:
            logger.info("开始更新稳定ID与group_id映射表...")
            
            # 0. 确保映射表存在
            logger.info("确保映射表存在...")
            db_manager.common_db.create_tables([StableIdGroupMapping], safe=True)
            
            # 1. 加载IbProduct表缓存
            self.load_ib_product_cache()
            
            # 2. 读取美股品种信息
            stock_info_df = self.read_stock_info_excel()
            if stock_info_df.empty:
                logger.error("无法读取美股品种信息，终止操作")
                return
            
            # 3. 读取重点核查信息（现在是稳定ID集合）
            focus_stable_ids = self.read_focus_check_excel()
            
            # 4. 构建映射数据
            mapping_data = self.build_mapping_data(stock_info_df, focus_stable_ids)
            
            if not mapping_data:
                logger.error("无法构建映射数据，终止操作")
                return
            
            # 5. 清空并重建映射表
            self.clear_mapping_table()
            self.save_mapping_data(mapping_data)
            
            logger.info("稳定ID与group_id映射表更新完成！")
            
            # 6. 输出统计信息
            total_mappings = len(mapping_data)
            priority_mappings = sum(1 for item in mapping_data if item['priority'])
            logger.info(f"映射表统计: 总计{total_mappings}条记录，其中{priority_mappings}条为重点核查")
            
        except Exception as e:
            logger.error(f"更新映射表时发生错误: {str(e)}")
            raise


def main():
    """主函数 - 可以单独运行此脚本"""
    # 配置日志
    try:
        # 创建管理器并执行更新
        manager = StableIdGroupMappingManager()
        manager.update_stable_id_group_mapping()
        
    except Exception as e:
        logger.error(f"执行映射表更新时发生致命错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()