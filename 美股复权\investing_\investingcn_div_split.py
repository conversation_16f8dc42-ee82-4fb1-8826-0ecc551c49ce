# 8/30/2025 更新
# 1. 表新增ib_symbol, conid两列. 先将investing网站symbol转化得到ib_symbol, 再从从ib_product表读到的字典里寻得conid
# 2. investing_rehab_merged表唯一性限制改为("symbol", "region", "ex_div_date", "description"), 针对多地同ticker上市公司同一天在不同地区分红/拆合股的情况(例如CRH 2025-08-22在英美同时分红)
# 3. 在merge_corporate_actions返回df前加入了("symbol", "region", "ex_div_date", "description")唯一性检验
# 4. 保留了入库时on_conflict_replace(), 方便覆盖数据 (investing似乎会某天查无当天数据,几天后又出现,可能是更新比较滞后. 每天可能需要下载前一段时间数据覆盖)
# 5. 企业微信通知表格中新增了conid列
# 6. 加入tqdm进度条, 去除一些多余log

# 9/1/2025 更新
# 1. 爬取地区从 中美港新英 改为 中美港日
# 2. 公司行动汇总表格过长时拆分为数个消息发出
# 3. 调整了数据库内列名顺序
# 4. 改用先删后增覆盖数据, 防止同一公司行动多条记录; 去除了写入时on_conflict_replace, 若违反唯一性要求就报错

from base64 import encode
from datetime import datetime
import pandas as pd
import numpy as np
import re
import json
import random
import time
from peewee import chunked

from tqdm import tqdm
from loguru import logger

import requests
from bs4 import BeautifulSoup

from utils.database_manager import db_manager, InvestingRehabOriginal, InvestingRehabMerged, IbProduct
from utils.wecom_alert import *

# Configure logger
logger.add("logs/investingcn_div_split.log", rotation="1 day", retention="30 days", level="INFO", encoding="utf-8")


def parse_chinese_date(chinese_date_str):
    """
    Parse Chinese date string like '2025年8月27日' to datetime object
    """
    if not chinese_date_str:
        return np.nan
    
    # Pattern to match Chinese date format: YYYY年MM月DD日
    pattern = r'(\d{4})年(\d{1,2})月(\d{1,2})日'
    match = re.match(pattern, chinese_date_str)
    
    if match:
        year = int(match.group(1))
        month = int(match.group(2))
        day = int(match.group(3))
        return datetime(year, month, day)
    
    return np.nan


def investing_symbol_to_ib_symbol(investing_symbol: str):
    # e.g. BRKa -> BRK A, GRP_u -> GRP U
    # Handle pattern: uppercase + underscore + lowercase (e.g. GRP_u -> GRP U)
    if '_' in investing_symbol:
        ib_symbol = re.sub(r'([A-Z]+)_([a-z]+)$', r'\1 \2', investing_symbol)
    else:
        # Handle pattern: uppercase + lowercase (e.g. BRKa -> BRK A)
        ib_symbol = re.sub(r'([A-Z]+)([a-z]+)$', r'\1 \2', investing_symbol)
    
    return ib_symbol.upper()


def get_ib_conid(ib_symbol: str, map: dict):
    """get conid from investing symbol"""
    try:
        return map[ib_symbol]
    except KeyError:
        return np.nan


def get_split_data(start_date, end_date, max_retries, conid_map):
    headers = {
        "accept": "*/*",
        "accept-language": "en-GB,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,en-US;q=0.6",
        "content-type": "application/x-www-form-urlencoded",
        "origin": "https://cn.investing.com",
        "priority": "u=1, i",
        "referer": "https://cn.investing.com/stock-split-calendar/",
        "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0",
        "x-requested-with": "XMLHttpRequest"
    }

    url = "https://cn.investing.com/stock-split-calendar/Service/getCalendarFilteredData"

    date_range = pd.date_range(start_date, end_date)
    daily_dfs = []
    no_results_days = ['以下日期无拆合股信息:']
    error_days= ['以下日期请求拆合股信息失败:']

    for date in tqdm(date_range, desc=f'获取{start_date}到{end_date}拆合股数据'):
        if date.weekday() >= 5:
            continue # sat and sun

        date_str = date.strftime("%Y-%m-%d")

        data = {
            "country[]": [
                "37",
                "35",
                "5",
                "39",
            ],
            "dateFrom": date_str,
            "dateTo": date_str,
            "currentTab": "custom",
            "limit_from": "0"
        }

        for i in range(max_retries):
            current_epoch = int(time.time())
            cookies = {
                "PHPSESSID": "dgv0njhmb9l1mqfiav2rnu9rg4",
                "page_equity_viewed": "0",
                "browser-session-counted": "true",
                "user-browser-sessions": "1",
                "adBlockerNewUserDomains": "**********",
                "udid": "f9aaac71835a7815b3fa6bc60000a9a2",
                "inudid": "f9aaac71835a7815b3fa6bc60000a9a2",
                "smd": "f9aaac71835a7815b3fa6bc60000a9a2-**********",
                "invab": "hardvidall_0|mwebe_1|navbarcta_5|noleftad_0|ovlayouta_-1|regwallb_0|tnbpopup_2",
                "__eventn_id": "f9aaac71835a7815b3fa6bc60000a9a2",
                "HMACCOUNT": "54DAE6C5F5EC43F5",
                "im_sharedid": "a7fdf4be-a2c7-4043-9997-c8f294552d9e",
                "im_sharedid_cst": "zix7LPQsHA%3D%3D",
                "_fbp": "fb.1.*************.*****************",
                "_cc_id": "290d0a5b61a667cfe778cc3cb308c4cf",
                "_lr_geo_location_state": "KWT",
                "_lr_geo_location": "HK",
                "ses_num": "2",
                "last_smd": "f9aaac71835a7815b3fa6bc60000a9a2-**********",
                "top_strip_variant": "%7B%22user_type%22%3A%22guest%22%2C%22variant_id%22%3A2%2C%22variant_name%22%3A%22Free%20users%202%2C%20FairValue%20(Overvalued)%22%7D",
                "r_p_s_n": "1",
                "uuid": "15811E6E-35D1-451E-961B-B44A7E9517AE",
                "proscore_card_opened": "1",
                "reg_trk_ep": "google%20one%20tap",
                "_gid": "GA1.2.**********.**********",
                "hb_insticator_uid": "973a2edc-fcae-4cdb-8fc8-dfb84dab58c9",
                "Hm_lvt_a1e3d50107c2a0e021d734fe76f85914": "1756175762,1756191474",
                "_dd_s": "aid=cdd56b98-4855-4b91-b9a5-abc391adbe50&logs=1&id=e370c72b-148e-4687-88d5-c64f6edd24ec&created=1756196024692&expire=1756196949900",
                "lifetime_page_view_count": "3",
                "g_state": "{\"i_p\":1756204424830,\"i_l\":1}",
                "geoC": "HK",
                "nyxDorf": "Y2c%2BbzNmM3E%2Fa29jN2YzLzZmNGw%2BPTsnYGNiZTA1",
                "page_view_count": "9",
                "cf_clearance": "qpVNoojB37EVT6E0Gicl7VGDHvHZ7zBaTAFBYKASEUA-1756260822-*******-0ohTIijgZA4MVOMaT4fW9mnQfj.LLyN65376yEb3zUu6ciTWC6en4POG2wVWaFIgBovBhSofGZvjSAshDqeBXYL0MZ929MdF_CQt6a1bi1KWNi23UpudrdrfAeuM8P2s5X4jiKIH63VDKBVIosdVpknmLhYBqL1ZH9qMMA8_0o7zeLn28NSO3A_s6YiPHlfo28eTc.0dWrZ0T97DA_YJ05fj5.k6gyUksoT1YxeydSg",
                "invpc": "12",
                "Hm_lpvt_a1e3d50107c2a0e021d734fe76f85914": f"{current_epoch - 17 * 60}",
                "_ga_C4NDLGKVMK": "GS2.1.s1756260821$o5$g1$t1756260823$j58$l0$h0",
                "_ga": "GA1.1.1233374257.**********",
                "cto_bundle": "Cfe2MF95NFV0a0FuZEF5NWt1NjhLVU96YzhpcmVDZExROGJmTVRIZnZxeDNKbjFzU3lXWGx3JTJGeEJtaXZ3Sk1FaVp6VWc0YTJkUGFCQjIlMkJiOGVyUEl2UWlFaWdSOTE3UmdoZ1F0N0dOY1lnR1ZsSFNiU1pjSXlObnh0NU1uNDdxTmxlVTV3TXBxVmdIVWhTeEhPZVA2OEFnalZ2ajVyYzQlMkIxTThyOWVhNUJMeDRzeXZCSWJpdmFQOFQlMkZ5c3dlanlUVzZRMg",
                "__cf_bm": "a1DIiZlf1fSELU7qiLGtYWyLVT2MnKeaLydBxQgp9uA-1756262816-*******-S7TuiI6kuSmZkaoloQERV3xdPRXpQOs_oGi5TolgWr4wH8mQfbDE3XJrapRGc9otncLVE4ES4FIOYBhWWZpoAEGsptAYsYeDJXEELKLGAqy1PRtso0XF5vox7yYoVC97",
                "_gat_allSitesTracker": "1",
                "__gads": f"ID=abf92a357b65d5fd:T=1756191155:RT={current_epoch}:S=ALNI_MZSUeYcGH-xTbilVqxe0DHkpK9lvQ",
                "__gpi": f"UID=0000118660710fa6:T=1756191155:RT={current_epoch}:S=ALNI_MadtFkzrCpcYG2dM2JFKzqbq_rxjg",
                "__eoi": f"ID=f12156d8447cf5d8:T=1756191155:RT={current_epoch}:S=AA-AfjYR0eoBMQljej2nh2cV72WT"
            }
            
            try:
                response = requests.post(url, headers=headers, cookies=cookies, data=data)
                response.raise_for_status()
                break
            except requests.exceptions.RequestException as e:
                logger.warning(f'hard requests error fetching split data for {date_str}, will retry: {e}')
                time.sleep(random.normalvariate(5, 1))
                continue
        else:
            logger.error(f'max retries exceeded when fetching split data for {date_str}')
            error_days.append(f'> `{date_str}`')
            continue

        soup = BeautifulSoup(response.json()['data'], 'html.parser')
        if soup.find('td', class_='noResults') is not None:
            no_results_days.append(f'> `{date_str}`')
            continue

        split_data = []
        current_date = ""
        rows = soup.find_all('tr')
        
        for row in rows:
            cells = row.find_all('td')
            if len(cells) == 3:
                # Get the date (first column)
                date_cell = cells[0].get_text(strip=True)
                if date_cell:  # If date is not empty, update current_date
                    current_date = date_cell
                    parsed_date = parse_chinese_date(date_cell)
                else:
                    parsed_date = parse_chinese_date(current_date)
                
                # Get company information (second column)
                company_cell = cells[1]
                company_name = company_cell.find('span', class_='inlineblock')
                company_name = company_name.get_text(strip=True) if company_name else np.nan
                
                # Get region from flag class
                flag_span = company_cell.find('span', class_='ceFlags')
                region = ""
                if flag_span:
                    flag_classes = flag_span.get('class', [])
                    for cls in flag_classes:
                        if cls not in ['ceFlags', 'middle']:
                            region = cls.replace('_', ' ')
                            break
                
                # Get stock symbol from the link
                symbol_link = company_cell.find('a', class_='bold')
                symbol = str(symbol_link.get_text(strip=True)) if symbol_link else np.nan

                # Get converted IB symbol and ConID
                ib_symbol = investing_symbol_to_ib_symbol(symbol)
                conid = get_ib_conid(ib_symbol, conid_map)
                
                # Get split ratio (third column)
                split_text = cells[2].get_text(strip=True)
                split = split_text.split(":")
                base = float(split[1])
                ert = float(split[0])
                split_ratio = base / ert
                
                # Only add if we have valid data
                if company_name and symbol and region and split_ratio and parsed_date:
                    if split_ratio < 1: #分股
                        split_data.append({
                            'ex_div_date': parsed_date,
                            'symbol': symbol,
                            'description': company_name,
                            'region': region,
                            'ib_symbol': ib_symbol,
                            'conid': conid,
                            'split_base': base,
                            'split_ert': ert,
                            'join_base': np.nan,
                            'join_ert': np.nan,
                            "base": base,
                            "ert": ert,
                            'split_ratio': split_ratio
                        })
                    elif split_ratio > 1: #合股
                        split_data.append({
                            'ex_div_date': parsed_date,
                            'symbol': symbol,
                            'description': company_name,
                            'region': region,
                            'ib_symbol': ib_symbol,
                            'conid': conid,
                            'split_base': np.nan,
                            'split_ert': np.nan,
                            'join_base': base,
                            'join_ert': ert,
                            "base": base,
                            "ert": ert,
                            'split_ratio': split_ratio
                        })
                else:
                    logger.warning(f'get_split_data: one or more among description/symbol/region/split_ratio/ex_div_date is NaN for row {row}')
            else:
                logger.warning(f'get_split_data: row does not contain the right number of cells for row {row}')
        
        # Convert to pandas DataFrame
        df = pd.DataFrame(split_data).sort_values(by='description').reset_index(drop=True)
        daily_dfs.append(df)

    final_df = pd.concat(daily_dfs, axis=0).sort_values(by=['ex_div_date', 'symbol'], ascending=[False, True]).reset_index(drop=True)
    final_df['symbol'] = final_df['symbol'].astype(str)
    final_df['ex_div_date'] = pd.to_datetime(final_df["ex_div_date"])
    
    # Combine no results and error days info
    split_info_parts = []
    if len(no_results_days) > 1:
        # Format each date on its own line with proper spacing
        formatted_no_results = "\n\n".join(no_results_days)
        split_info_parts.append(formatted_no_results)
    if len(error_days) > 1:
        # Format each date on its own line with proper spacing
        formatted_error_days = "\n\n".join(error_days)
        split_info_parts.append(formatted_error_days)
    
    split_info = "\n\n".join(split_info_parts) if split_info_parts else "> 无"
    return final_df, split_info


def get_div_data(start_date, end_date, max_retries, conid_map):

    headers = {
        "accept": "*/*",
        "accept-language": "en-GB,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,en-US;q=0.6",
        "content-type": "application/x-www-form-urlencoded",
        "origin": "https://cn.investing.com",
        "priority": "u=1, i",
        "referer": "https://cn.investing.com/dividends-calendar/",
        "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0",
        "x-requested-with": "XMLHttpRequest"
    }

    url = "https://cn.investing.com/dividends-calendar/Service/getCalendarFilteredData"

    date_range = pd.date_range(start_date, end_date)
    daily_dfs = []
    no_results_days = ['以下日期无分红信息:']
    error_days = ['以下日期请求分红信息失败:']

    for date in tqdm(date_range, desc=f'获取{start_date}到{end_date}分红数据'):
        if date.weekday() >= 5:
            continue # sat and sun

        date_str = date.strftime("%Y-%m-%d")

        data = {
            "country[]": [
                "37",
                "35",
                "5",
                "39"
            ],
            "dateFrom": date_str,
            "dateTo": date_str,
            "currentTab": "custom",
            "limit_from": "0"
        }

        for i in range(max_retries):
            current_epoch = int(time.time())
            cookies = {
                "PHPSESSID": "dgv0njhmb9l1mqfiav2rnu9rg4",
                "geoC": "HK",
                "page_equity_viewed": "0",
                "browser-session-counted": "true",
                "user-browser-sessions": "1",
                "adBlockerNewUserDomains": "**********",
                "udid": "f9aaac71835a7815b3fa6bc60000a9a2",
                "inudid": "f9aaac71835a7815b3fa6bc60000a9a2",
                "smd": "f9aaac71835a7815b3fa6bc60000a9a2-**********",
                "invab": "hardvidall_0|mwebe_1|navbarcta_5|noleftad_0|ovlayouta_-1|regwallb_0|tnbpopup_2",
                "__eventn_id": "f9aaac71835a7815b3fa6bc60000a9a2",
                "HMACCOUNT": "54DAE6C5F5EC43F5",
                "_imntz_error": "0",
                "im_sharedid": "a7fdf4be-a2c7-4043-9997-c8f294552d9e",
                "im_sharedid_cst": "zix7LPQsHA%3D%3D",
                "_fbp": "fb.1.*************.*****************",
                "_cc_id": "290d0a5b61a667cfe778cc3cb308c4cf",
                "panoramaId_expiry": "*************",
                "panoramaId": "5d076197548daa36c4e78da4c30ea9fb927ac5f391b6969f01da72a864604c49",
                "panoramaIdType": "panoDevice",
                "_lr_geo_location_state": "KWT",
                "_lr_geo_location": "HK",
                "ses_num": "2",
                "last_smd": "f9aaac71835a7815b3fa6bc60000a9a2-**********",
                "top_strip_variant": "%7B%22user_type%22%3A%22guest%22%2C%22variant_id%22%3A2%2C%22variant_name%22%3A%22Free%20users%202%2C%20FairValue%20(Overvalued)%22%7D",
                "r_p_s_n": "1",
                "adsFreeSalePopUp": "3",
                "uuid": "15811E6E-35D1-451E-961B-B44A7E9517AE",
                "proscore_card_opened": "1",
                "reg_trk_ep": "google%20one%20tap",
                "_gid": "GA1.2.**********.**********",
                "hb_insticator_uid": "973a2edc-fcae-4cdb-8fc8-dfb84dab58c9",
                "Hm_lvt_a1e3d50107c2a0e021d734fe76f85914": "1756175762,1756191474",
                "_dd_s": "aid=cdd56b98-4855-4b91-b9a5-abc391adbe50&logs=1&id=e370c72b-148e-4687-88d5-c64f6edd24ec&created=1756196024692&expire=1756196949900",
                "lifetime_page_view_count": "3",
                "__cf_bm": "52Hn7qrWHoItFQRra6QBw9PxrE19Oe2LyeCGk3i_4.o-1756196980-*******-m4Addzr1RIHUSpI1y7mJ3Yc5GuMEa6rJEwc3K5CP6_Ihf_r_4fmuuategqNRftG4cJf4Y1qxXKXgeYrJ.C0l2tEzJk.kBi8k9XKxKvJOWt3Tw43OfX59LHYEC9jJvr80",
                "cf_clearance": "s70hSOEszf0V2T13dfoa2JlItcInEYm02fwplm2xMvI-1756197017-*******-pOoLYmsMJ8slFWIs1_OYAYnDvwWdKNh5VE0VP.GEho5MSZ3UAk3hqSwdy2IsYSP_KVVQLtdJ0RnQa_tck7dj9ubV1ZiqJkwBM2IarDZpGw_xc0oxs.nwVK29sDCqpzbia7VqnLxIj1zjnhIyKDEZKrP5IKhT4CY_PpdXJywHGict0Cwnm5K3_ZO0KewWX6zQW5aOevA.Bq5WevVoK2djD5DawzV6RFNfit5ePFIQAxs",
                "g_state": "{\"i_p\":1756204424830,\"i_l\":1}",
                "__gads": f"ID=abf92a357b65d5fd:T=1756191155:RT={current_epoch}:S=ALNI_MZSUeYcGH-xTbilVqxe0DHkpK9lvQ", #
                "__gpi": f"UID=0000118660710fa6:T=1756191155:RT={current_epoch}:S=ALNI_MadtFkzrCpcYG2dM2JFKzqbq_rxjg", #
                "__eoi": f"ID=f12156d8447cf5d8:T=1756191155:RT={current_epoch}:S=AA-AfjYR0eoBMQljej2nh2cV72WT", #
                "nyxDorf": "MTUxYDVgMnBhNW5iZDUxLT5uZDxkZzcrMzAwNzcy",
                "invpc": "9",
                "page_view_count": "1",
                "Hm_lpvt_a1e3d50107c2a0e021d734fe76f85914": f"{current_epoch - 13 * 60}",#
                "_ga": "GA1.1.1233374257.**********",
                "cto_bundle": "_dsvdV95NFV0a0FuZEF5NWt1NjhLVU96YzhxZkZJUkpNY2Vsc211cEVIMmlDNkdVSHhpb3lQV05DS0lEZE1xZHpoJTJGTjB0aXRaRVJHUktUQ1RsRWRUWmhSQXElMkZDSkVKRGJQbUhld1E4aXlXam1jenBFejBxWld5akxGVmNDMDJINHprU0R0WUNZY0gwMElKZGRRNVU4WTFEVVVLN0llVjdSb3hOanJTRk9ZUkhyamoyVVVlbjN5eWsxa3poSWJRNDZyWXpK",
                "_ga_C4NDLGKVMK": "GS2.1.s1756196081$o4$g1$t1756197806$j60$l0$h0",
                "_gat_allSitesTracker": "1"
            }
            try:
                response = requests.post(url, headers=headers, cookies=cookies, data=data)
                response.raise_for_status()
                break
            except requests.exceptions.RequestException as e:
                logger.warning(f'hard requests error fetching dividend data for {date_str}, will retry: {e}')
                time.sleep(random.normalvariate(5, 1))
                continue
        else:
            logger.error(f'max retries exceeded when fetching dividend data for {date_str}')
            error_days.append(f'> `{date_str}`')
            continue

        soup = BeautifulSoup(response.json()['data'], 'html.parser')
        if soup.find('td', class_='noResults') is not None:
            no_results_days.append(f'> `{date_str}`')
            continue
                     
        dividend_data = []
        rows = soup.find_all('tr')
        for row in rows:

            cells = row.find_all('td')

            if "theDay" in cells[0].get('class', []): # Skip divider rows that just show dates
                continue

            if len(cells) == 7: 
                # Get company information (second column)
                company_cell = cells[1]
                company_name = company_cell.find('span', class_='earnCalCompanyName')
                company_name = company_name.get_text(strip=True) if company_name else np.nan
                
                # Get region from flag class (first column)
                flag_cell = cells[0]
                flag_span = flag_cell.find('span', class_='ceFlags')
                region = ""
                if flag_span:
                    flag_classes = flag_span.get('class', [])
                    for cls in flag_classes:
                        if cls not in ['ceFlags', 'middle']:
                            region = cls.replace('_', ' ')
                            break
                
                # Get stock symbol from the link
                symbol_link = company_cell.find('a', class_='bold')
                symbol = str(symbol_link.get_text(strip=True)) if symbol_link else np.nan

                # Get converted IB symbol and ConID
                ib_symbol = investing_symbol_to_ib_symbol(symbol)
                conid = get_ib_conid(ib_symbol, conid_map)
                
                # Get ex-dividend date (third column)
                ex_div_date_text = cells[2].get_text(strip=True)
                ex_div_date = parse_chinese_date(ex_div_date_text)
                
                # Get dividend amount (fourth column)
                dividend_amount_text = cells[3].get_text(strip=True)
                dividend_amount = float(dividend_amount_text) if dividend_amount_text.replace('.', '').replace('-', '').isdigit() else np.nan
                
                # Get dividend type (fifth column)
                type_cell = cells[4]
                type_span = type_cell.find('span')
                dividend_type = type_span.get('class', '')[0][4:] if type_span else np.nan
                
                # Get payment date (sixth column)
                payment_date_text = cells[5].get_text(strip=True)
                payment_date = parse_chinese_date(payment_date_text)
                
                # Get yield (seventh column)
                yield_text = cells[6].get_text(strip=True)
                yield_value = round(float(yield_text.replace('%', '')) / 100, 6) if yield_text != '-' and yield_text.replace('.', '').replace('%', '').replace('-', '').isdigit() else np.nan
                
                # Only add if we have valid data
                if company_name and symbol and ex_div_date and region and payment_date and dividend_amount:
                    dividend_data.append({
                        'ex_div_date': ex_div_date,
                        'symbol': symbol,
                        'description': company_name,
                        'region': region,
                        'ib_symbol': ib_symbol,
                        'conid': conid,
                        'div_amount': dividend_amount,
                        'div_type': dividend_type,
                        'pmt_date': payment_date,
                        'div_yield': yield_value
                    })
                else:
                    logger.warning(f'get_div_data: one or more among description/symbol/region/div_amount/ex_div_date/pmt_date is NaN for row {row}')
            else:
                logger.warning(f'get_div_data: wrong cell count for non-tablesorterdivider row {row}')
        
        # Convert to pandas DataFrame
        df = pd.DataFrame(dividend_data).sort_values(by='description').reset_index(drop=True)
        daily_dfs.append(df)

    final_df = pd.concat(daily_dfs, axis = 0).sort_values(by=['ex_div_date', 'symbol'], ascending=[False, True]).reset_index(drop=True)
    final_df['symbol'] = final_df['symbol'].astype(str)
    final_df['ex_div_date'] = pd.to_datetime(final_df["ex_div_date"])
    
    # Combine no results and error days info
    div_info_parts = []
    if len(no_results_days) > 1:
        # Format each date on its own line with proper spacing
        formatted_no_results = "\n\n".join(no_results_days)
        div_info_parts.append(formatted_no_results)
    if len(error_days) > 1:
        # Format each date on its own line with proper spacing
        formatted_error_days = "\n\n".join(error_days)
        div_info_parts.append(formatted_error_days)
    
    div_info = "\n\n".join(div_info_parts) if div_info_parts else "> 无"
    return final_df, div_info


def merge_corporate_actions(rehab_df):
    """
    Simple merge using base and ert columns
    """
    # Group by company and date, including conid
    grouped = rehab_df.groupby(['symbol', 'description', 'ex_div_date', 'region']).agg({
        # Dividends
        'div_amount': lambda x: x.sum() if x.notna().any() else np.nan,
        'div_type': lambda x: '; '.join(x.dropna().unique()) if x.notna().any() else np.nan,
        'pmt_date': lambda x: x.dropna().iloc[0] if x.notna().any() else np.nan,
        'div_yield': lambda x: x.sum() if x.notna().any() else np.nan,
        
        # Splits - use base and ert columns directly
        'base': lambda x: x.prod() if x.notna().any() else np.nan,
        'ert': lambda x: x.prod() if x.notna().any() else np.nan,
        'split_ratio': lambda x: x.prod() if x.notna().any() else np.nan,
        
        # Conid - take the first non-nan value, or nan if all are nan
        'ib_symbol': lambda x: x.dropna().iloc[0] if x.notna().any() else np.nan,
        'conid': lambda x: x.dropna().iloc[0] if x.notna().any() else np.nan,
    }).reset_index()
    
    # Add action count after aggregation
    grouped['action_count'] = rehab_df.groupby(['symbol', 'description', 'ex_div_date', 'region']).size().values
    
    # Classify as split or join based on final ratio
    def classify_action(row):
        if pd.notna(row['split_ratio']):
            if row['split_ratio'] < 1:  # Split
                return pd.Series({
                    'split_base': row['base'],
                    'split_ert': row['ert'],
                    'join_base': np.nan,
                    'join_ert': np.nan
                })
            else:  # Join
                return pd.Series({
                    'split_base': np.nan,
                    'split_ert': np.nan,
                    'join_base': row['base'],
                    'join_ert': row['ert']
                })
        else:
            return pd.Series({
                'split_base': np.nan,
                'split_ert': np.nan,
                'join_base': np.nan,
                'join_ert': np.nan
            })
    
    # Apply classification
    action_classification = grouped.apply(classify_action, axis=1)
    
    # Combine results
    final_df = pd.concat([grouped, action_classification], axis=1).sort_values(by=['ex_div_date', 'region', 'symbol'], ascending=[False, True, True]).reset_index(drop=True)
    
    # Check for duplicates in final output based on (symbol, region, ex_div_date)
    duplicate_mask = final_df.duplicated(subset=['symbol', 'region', 'description', 'ex_div_date'], keep=False)
    if duplicate_mask.any():
        duplicate_records = final_df[duplicate_mask]
        logger.error(f"Found duplicate records in final output based on (symbol, region, ex_div_date):")
        logger.error(duplicate_records[['symbol', 'region', 'ex_div_date', 'description']].to_string())
    
    return final_df


def get_summary_markdown(rehab_df, date_str):
    """
    Generate markdown table segments for corporate actions data.
    Returns a list of markdown segments, each not exceeding 4096 characters.
    """
    day = datetime.strptime(date_str, "%Y-%m-%d").date()
    
    # Filter data for the day
    day_data = rehab_df.loc[rehab_df['ex_div_date'].dt.date == day, :]
    
    if day_data.empty:
        return [f"# {date_str} 公司行动数据\n\n此日无分红或拆合股数据"]
    
    # Create table header
    table_header = f"# {date_str} 公司行动数据\n\n"
    table_header += "| 代码 | 地区 | 公司名称 | IB代码 | ConID | 拆合股比例 | 分红金额 |\n"
    table_header += "|------|------|----------|--------|-------|------------|----------|\n"
    
    # Prepare all table rows
    table_rows = []
    for _, row in day_data.iterrows():
        # Extract and format data
        symbol = str(row.get('symbol', '')).strip()
        region = str(row.get('region', '')).strip()
        description = str(row.get('description', '')).strip()
        
        # Handle split/join ratio
        split_ratio = ''
        if pd.notna(row.get('split_ratio')):
            split_ratio = str(row.get('split_ratio'))
        
        # Handle dividend amount
        div_amount = ''
        if pd.notna(row.get('div_amount')):
            div_amount = f"{row.get('div_amount'):.4f}"
        
        # Handle conid
        conid = ''
        if pd.notna(row.get('conid')):
            conid = str(int(row.get('conid')))
        
        # Handle ib_symbol
        ib_symbol = ''
        if pd.notna(row.get('ib_symbol')):
            ib_symbol = str(row.get('ib_symbol'))
        
        # Escape pipe characters for markdown
        symbol = symbol.replace('|', '\\|')
        region = region.replace('|', '\\|')
        description = description.replace('|', '\\|')
        ib_symbol = ib_symbol.replace('|', '\\|')
        split_ratio = split_ratio.replace('|', '\\|')
        div_amount = div_amount.replace('|', '\\|')
        conid = conid.replace('|', '\\|')
        
        table_rows.append(f"| {symbol} | {region} | {description} | {ib_symbol} | {conid} | {split_ratio} | {div_amount} |")
    
    # Split into segments not exceeding 4096 characters
    segments = []
    current_segment = table_header
    rows_in_current_segment = 0
    
    for row in table_rows:
        # Check if adding this row would exceed 4096 characters
        test_segment = current_segment + row + "\n"
        if len(test_segment) > 3500:
            # Current segment is full, save it and start a new one
            segments.append(current_segment.rstrip())
            current_segment = table_header + row + "\n"
            rows_in_current_segment = 1
        else:
            # Add row to current segment
            current_segment = test_segment
            rows_in_current_segment += 1
    
    # Add the last segment if it has content
    if current_segment != table_header:
        segments.append(current_segment.rstrip())
    
    # If no segments were created (empty table), return a single segment
    if not segments:
        segments = [f"# {date_str} 公司行动数据\n\n此日无分红或拆合股数据"]
    
    return segments


def load_ib_products():
    """load symbol-conid map from ib_product table"""
    symbol_conid_map = {}
    try:
        query = IbProduct.select(IbProduct.symbol, IbProduct.conid).where(
            IbProduct.is_latest == True
        ).order_by(IbProduct.created_time.asc())
        for record in query:
            if record.symbol and record.conid:
                symbol_conid_map[record.symbol] = record.conid
        logger.info(f"loaded {len(symbol_conid_map)} symbol-conid mapping relations")
    except Exception as e:
        logger.error(f"error when loading symbol-conid mapping: {str(e)}\n{traceback.format_exc()}")
    return symbol_conid_map


def main():
    logger.info("Starting Investing.com dividend and split data collection process")

    today = datetime.now().strftime("%Y-%m-%d")
    start = "2024-01-01"
    end = today
    retries = 3

    wecom_key = "ee0b6801-f2c5-4811-ba1f-227b543b3459"

    symbol_conid_map = load_ib_products() # 目前只有美股？

    message1 = []
    message1.append(f"# {today} 英为财情汇总")
    message1.append(f"下载起始日期: `{start}`")
    message1.append(f"下载截止日期: `{end}`")

    message1.append('---')

    message1.append(f"## 分红数据特殊情况")
    logger.info(f"Collecting dividend data from {start} to {end}")
    div_df, div_info = get_div_data(start, end, retries, symbol_conid_map)
    logger.info(f"Collected {len(div_df)} dividend records")
    message1.append(div_info)

    message1.append('---')
    
    message1.append(f"## 拆合股数据特殊情况")
    logger.info(f"Collecting split data from {start} to {end}")
    split_df, split_info = get_split_data(start, end, retries, symbol_conid_map)
    logger.info(f"Collected {len(split_df)} split records")
    message1.append(split_info)

    message1.append('---')
    
    rehab_df = pd.concat([div_df, split_df], axis=0, join='outer', ignore_index=True).sort_values(by=['ex_div_date', 'region', 'symbol'], ascending=[False, True, True]).reset_index(drop=True)
    logger.info(f"Raw dataset contains {len(rehab_df)} total records")
    
    rehab_df_merged = merge_corporate_actions(rehab_df)
    logger.info(f"Merged dataset contains {len(rehab_df_merged)} same-day same-stock corporate action records")

    assert rehab_df_merged['action_count'].sum() == len(rehab_df) # ensure all corporate actions are recorded in merged df

    logger.info("Saving data to CSV files")
    rehab_df.to_csv('output/rehab_df_original.csv', encoding='UTF-8')
    rehab_df_merged.to_csv('output/rehab_df_merged.csv', encoding='UTF-8')
    div_df.to_csv('output/div_preview.csv', encoding='UTF-8')
    split_df.to_csv('output/split_preview.csv', encoding='UTF-8')
    
    logger.info("Saving info files")
    with open('output/div_info.txt', 'w', encoding='UTF-8') as f:
        f.write(div_info)
    with open('output/split_info.txt', 'w', encoding='UTF-8') as f:
        f.write(split_info)

    logger.info("Preparing data for database insertion...")
    db_records = rehab_df.replace({np.nan: None}).to_dict("records") # np.nan was not accepted
    db_records_merged = rehab_df_merged.replace({np.nan: None}).to_dict("records") # np.nan was not accepted
    # save original table
    message1.append(f"## 数据库")
    logger.info("Starting database operations")
    try:
        if not InvestingRehabOriginal.table_exists():
            logger.info("Investing分红和拆合股数据库表(原数据)不存在, 将创建新表")
            InvestingRehabOriginal.create_table()

        deleted_count = InvestingRehabOriginal.delete().where(
        InvestingRehabOriginal.ex_div_date.between(start, end)
        ).execute()
        logger.info(f"从investing_rehab_original表删除了{deleted_count}条原有记录")

        with db_manager.common_db.atomic():
            for batch in chunked(db_records, 50):
                InvestingRehabOriginal.insert_many(batch).execute()
        logger.info(f"向investing_rehab_original表插入了{len(db_records)}条记录")
        message1.append(f"investing_rehab_original表共入库{len(db_records)}条记录")

        if not InvestingRehabMerged.table_exists():
            logger.info("Investing分红和拆合股数据库表(同天同股公司行动合并)不存在, 将创建新表")
            InvestingRehabMerged.create_table()

        deleted_count = InvestingRehabMerged.delete().where(
        InvestingRehabMerged.ex_div_date.between(start, end)
        ).execute()
        logger.info(f"从investing_rehab_merged表删除了{deleted_count}条原有记录")

        with db_manager.common_db.atomic():
            for batch in chunked(db_records_merged, 50):
                InvestingRehabMerged.insert_many(batch).execute()
        logger.info(f"向investing_rehab_merged表插入了{len(db_records_merged)}条记录")
        message1.append(f"investing_rehab_merged表共入库{len(db_records_merged)}条记录")
        
        logger.info("Investing分红和拆合股数据批量入库成功")
    except Exception as e:
        logger.error(f"Investing分红和拆合股数据批量入库失败: {e}")
        message1.append(f"入库失败, 请检查")

    message2_segments = get_summary_markdown(rehab_df, end)

    logger.info("database operations completed successfully, reporting to WeCom...")

    ok1, e1 = report_we_alert(msgtype="markdown_v2", key=wecom_key, markdown_v2_content='\n\n'.join(message1), text="")
    if ok1:
        logger.info("download summary sent to WeCom successfully")
    elif not ok1:
        logger.error(f"Error when sending download summary to WeCom: {e1}")

    # Send each table segment separately
    for i, segment in enumerate(message2_segments):
        segment_number = f" ({i+1}/{len(message2_segments)})" if len(message2_segments) > 1 else ""
        segment_with_number = segment.replace("公司行动数据", f"公司行动数据{segment_number}")
        
        ok2, e2 = report_we_alert(msgtype="markdown_v2", key=wecom_key, markdown_v2_content=segment_with_number, text="")
        if ok2:
            logger.info(f"summary table segment sent to WeCom successfully ({i+1}/{len(message2_segments)})")
        elif not ok2:
            logger.error(f"Error when sending summary table segment {i+1}/{len(message2_segments)} to WeCom: {e2}")
    
if __name__ == '__main__':
    main()