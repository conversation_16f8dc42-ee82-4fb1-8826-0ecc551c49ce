"""
检测股数变动脚本
从target_db.overview获取conid -> ib_product获取symbol -> 转为futu_code -> futu_rehab查询复权因子
"""
import pandas as pd
from datetime import datetime
import sys
import os

# 添加路径以导入utils模块
# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from utils.database_manager import db_manager, FutuRehab, IbProduct
from utils.mysql_database import create_mysql_database
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.utility import load_json
from loguru import logger

class ShareChangeChecker:
    """股数变动检查器"""
    
    def __init__(self, target_db_name: str = "vnpy_stk_us_ib_m_2206_250814"):
        """
        初始化
        Args:
            target_db_name: 目标数据库名称
        """
        self.target_db_name = target_db_name
        
        # 加载数据库配置并创建目标数据库连接
        db_setting = load_json("vt_setting.json")
        db_setting["database.database"] = target_db_name
        self.target_db, self.DbBarData, self.DbTickData, self.DbBarOverview, self.DbTickOverview = create_mysql_database(db_setting)
        
        # 检查时间范围
        self.check_start_date = datetime(2024, 6, 1)
        self.check_end_date = datetime(2025, 8, 16)
        
        logger.info(f"初始化股数变动检查器，目标数据库: {target_db_name}")
        logger.info(f"检查时间范围: {self.check_start_date} 到 {self.check_end_date}")
    
    def get_conids_from_overview(self):
        """从target_db的overview表获取所有conid"""
        logger.info("从overview表获取conid集合...")
        
        overviews = self.target_db.get_bar_overview()
        conids = set()
        
        for overview in overviews:
            if overview.symbol.isdigit():  # conid都是数字
                conids.add(int(overview.symbol))
        
        logger.info(f"从overview表获取到 {len(conids)} 个conid")
        return list(conids)
    
    def get_ib_symbols(self, conids):
        """从ib_product表获取对应的ib_symbol，使用group_id找到最新的symbol"""
        logger.info(f"从ib_product表查询 {len(conids)} 个conid的最新ib_symbol...")
        
        # 1. 查询old_conids对应的group_id
        old_products = (IbProduct
                       .select(IbProduct.conid, IbProduct.group_id, IbProduct.symbol)
                       .where(IbProduct.conid.in_(conids)))
        
        # 2. 获取所有最新的conid、group_id和symbol
        latest_products = (IbProduct
                          .select(IbProduct.conid, IbProduct.group_id, IbProduct.symbol)
                          .where(IbProduct.is_latest == True))
        
        # 3. 构建映射关系
        conid_to_group_id = {p.conid: {'group_id': p.group_id, 'symbol': p.symbol} 
                             for p in old_products if p.group_id}
        group_id_to_latest = {p.group_id: {'conid': p.conid, 'symbol': p.symbol} 
                              for p in latest_products if p.group_id}
        latest_conids = {p.conid for p in latest_products}
        
        results = []
        
        for conid in conids:
            if conid in latest_conids:
                # conid已经是最新的，直接使用
                old_info = conid_to_group_id.get(conid)
                if old_info:
                    results.append({
                        'conid': conid,
                        'ib_symbol': old_info['symbol']
                    })
            else:
                # conid不是最新的，通过group_id找到最新的
                old_info = conid_to_group_id.get(conid)
                if old_info:
                    group_id = old_info['group_id']
                    latest_info = group_id_to_latest.get(group_id)
                    if latest_info:
                        results.append({
                            'conid': conid,
                            'ib_symbol': latest_info['symbol'],  # 使用最新的symbol
                            'latest_conid': latest_info['conid']  # 记录最新的conid供参考
                        })
                    else:
                        # 没有找到最新的，使用原来的
                        results.append({
                            'conid': conid,
                            'ib_symbol': old_info['symbol']
                        })
        
        logger.info(f"找到 {len(results)} 个conid对应的ib_symbol")
        return results
    
    def convert_to_futu_codes(self, ib_symbols_data):
        """转换为futu_code格式"""
        logger.info("转换ib_symbol为futu_code格式...")
        
        futu_data = []
        for item in ib_symbols_data:
            conid = item['conid']
            ib_symbol = item['ib_symbol']
            
            if ib_symbol:
                # futu_code格式: US.{ib_symbol.replace(" ", ".")}
                futu_code = f"US.{ib_symbol.replace(' ', '.')}"
                futu_data.append({
                    'conid': conid,
                    'ib_symbol': ib_symbol,
                    'futu_code': futu_code
                })
        
        logger.info(f"转换得到 {len(futu_data)} 个futu_code")
        return futu_data
    
    def query_futu_rehab_factors(self, futu_data):
        """查询futu_rehab表中的复权因子"""
        logger.info(f"查询futu_rehab表中 {len(futu_data)} 个标的的复权因子...")
        
        # 提取所有futu_code
        futu_codes = [item['futu_code'] for item in futu_data]
        
        # 查询futu_rehab表
        query = (FutuRehab
                .select()
                .where(
                    (FutuRehab.code.in_(futu_codes)) &
                    (FutuRehab.ex_div_date >= self.check_start_date) &
                    (FutuRehab.ex_div_date <= self.check_end_date)
                ))
        
        results = []
        share_change_count = 0
        
        for rehab in query:
            # 检查是否有股数变动 (share_factor != 1)
            has_share_change = rehab.share_factor != 1.0 if rehab.share_factor is not None else False
            
            if has_share_change:
                share_change_count += 1
            
            results.append({
                'futu_code': rehab.code,
                'ex_div_date': rehab.ex_div_date,
                'share_factor': rehab.share_factor,
                'div_factor': rehab.div_factor,
                'combined_factor': rehab.combined_factor,
                'has_share_change': has_share_change,
                # 其他字段
                'prev_close': rehab.prev_close,
                'per_cash_div': rehab.per_cash_div,
                'split_ratio': rehab.split_ratio,
                'per_share_div_ratio': rehab.per_share_div_ratio,
                'per_share_trans_ratio': rehab.per_share_trans_ratio,
                'allotment_ratio': rehab.allotment_ratio,
                'stk_spo_ratio': rehab.stk_spo_ratio
            })
        
        logger.info(f"查询到 {len(results)} 条复权记录，其中 {share_change_count} 条有股数变动")
        return results
    
    def merge_data(self, futu_data, rehab_results):
        """合并数据，添加conid和ib_symbol信息"""
        logger.info("合并数据...")
        
        # 创建futu_code到conid/ib_symbol的映射
        futu_mapping = {item['futu_code']: item for item in futu_data}
        
        # 为复权数据添加conid和ib_symbol
        for rehab in rehab_results:
            futu_code = rehab['futu_code']
            if futu_code in futu_mapping:
                rehab['conid'] = futu_mapping[futu_code]['conid']
                rehab['ib_symbol'] = futu_mapping[futu_code]['ib_symbol']
            else:
                rehab['conid'] = None
                rehab['ib_symbol'] = None
        
        return rehab_results
    
    def save_to_csv(self, data, filename=None):
        """保存结果到CSV文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"share_changes_{timestamp}.csv"
        
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        if data:
            df = pd.DataFrame(data)
            # 重新排序列，重要信息在前
            cols = ['conid', 'ib_symbol', 'futu_code', 'ex_div_date', 'has_share_change', 'share_factor']
            other_cols = [col for col in df.columns if col not in cols]
            df = df[cols + other_cols]
            
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"结果已保存到: {filepath}")
            logger.info(f"共 {len(df)} 条记录，其中有股数变动的: {df['has_share_change'].sum()} 条")
            
            # 输出有股数变动的标的统计
            if df['has_share_change'].any():
                share_change_symbols = df[df['has_share_change']]['ib_symbol'].unique()
                logger.info(f"有股数变动的标的 ({len(share_change_symbols)} 个): {list(share_change_symbols)}")
        else:
            logger.info("无数据需要保存")
        
        return filepath
    
    def run(self):
        """执行完整的检查流程"""
        logger.info("开始执行股数变动检查...")
        
        try:
            # 1. 获取conid集合
            conids = self.get_conids_from_overview()
            if not conids:
                logger.warning("未找到任何conid，退出")
                return
            
            # 2. 获取ib_symbol
            ib_symbols_data = self.get_ib_symbols(conids)
            if not ib_symbols_data:
                logger.warning("未找到任何ib_symbol，退出")
                return
            
            # 3. 转换为futu_code
            futu_data = self.convert_to_futu_codes(ib_symbols_data)
            if not futu_data:
                logger.warning("未转换出任何futu_code，退出")
                return
            
            # 4. 查询复权因子
            rehab_results = self.query_futu_rehab_factors(futu_data)
            
            # 5. 合并数据
            merged_data = self.merge_data(futu_data, rehab_results)
            
            # 6. 保存到CSV
            csv_path = self.save_to_csv(merged_data)
            
            logger.info("股数变动检查完成！")
            return csv_path
            
        except Exception as e:
            logger.error(f"执行过程中出现错误: {e}")
            raise


if __name__ == "__main__":
    checker = ShareChangeChecker()
    checker.run()