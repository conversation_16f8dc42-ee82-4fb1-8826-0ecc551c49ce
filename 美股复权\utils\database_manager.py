from datetime import datetime
from peewee import (AutoField, CharField, DateTimeField, DoubleField, Model, MySQLDatabase as PeeweeMySQLDatabase,
                    BigIntegerField, IntegerField, BooleanField, DoubleField, SQL)
from playhouse.shortcuts import ReconnectMixin
from vnpy.trader.utility import load_json
import re
from typing import List, Optional
import traceback
from loguru import logger
import queue
import threading
import time
from vnpy.trader.database import ZoneInfo

# 预定义常用时区对象
TZ_UTC = ZoneInfo('UTC')
TZ_ASIA_SHANGHAI = ZoneInfo('Asia/Shanghai')
TZ_AMERICA_NEWYORK = ZoneInfo('America/New_York')

# 时区映射字典
TIMEZONE_MAP = {
    'UTC': TZ_UTC,
    'Asia/Shanghai': TZ_ASIA_SHANGHAI,
    'America/New_York': TZ_AMERICA_NEWYORK
}

def dbtz_convert(dt: Optional[datetime], from_tz: str, to_tz: str) -> Optional[datetime]:
    """在不同时区之间转换时间（用于数据库中不带时区信息的datetime）
    
    Args:
        dt: 要转换的datetime对象（不带时区信息）
        from_tz: 原时区，如 'UTC', 'Asia/Shanghai' 等
        to_tz: 目标时区，如 'America/New_York' 等
        
    Returns:
        datetime: 转换后的datetime对象（不带时区信息），如果输入为None则返回None
    """
    if not dt:
        return None
        
    try:
        # 从映射中获取时区对象
        from_tz_info = TIMEZONE_MAP.get(from_tz)
        to_tz_info = TIMEZONE_MAP.get(to_tz)
        
        if not from_tz_info or not to_tz_info:
            # 如果没有预定义的时区对象，则动态创建（作为后备方案）
            from_tz_info = ZoneInfo(from_tz)
            to_tz_info = ZoneInfo(to_tz)
        
        # 添加原时区信息
        dt = dt.replace(tzinfo=from_tz_info)
        
        # 转换到目标时区并移除时区信息
        return dt.astimezone(to_tz_info).replace(tzinfo=None)
    except Exception as e:
        return None

class ReconnectMySQLDatabase(ReconnectMixin, PeeweeMySQLDatabase):
    """带有重连混入的MySQL数据库类"""
    pass

class DatabaseManager:
    """数据库管理器，单例模式"""
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            # 加载数据库配置
            self.common_info_settings = load_json("vt_setting_remote.json")
            # self.history_settings = load_json("vt_setting_wind.json")

            # 初始化数据库连接
            self.common_db = self._init_database(self.common_info_settings)
            # self.history_db = self._init_database(self.history_settings)

            DatabaseManager._initialized = True

    def _init_database(self, settings: dict) -> ReconnectMySQLDatabase:
        """初始化数据库连接"""
        db = ReconnectMySQLDatabase(
            database=settings["database.database"],
            user=settings["database.user"],
            password=settings["database.password"],
            host=settings["database.host"],
            port=settings["database.port"]
        )
        return db

    def batch_save_to_db(self, data_list: List[dict], model_class: Model, primary_key: str = "conid", ignore_fields: Optional[List[str]] = None, replace: bool = False) -> None:
        """批量保存数据到数据库
        
        Args:
            data_list: 要保存的数据列表
            model_class: Peewee模型类
            primary_key: 主键字段名，默认为"conid"
            ignore_fields: 需要忽略的字段列表，默认为None（会被替换为["create_time", "update_time"]）
            replace: 是否使用replace模式进行批量保存，默认为False (即on duplicate key update)
        """
        try:
            if not data_list:
                return
                
            table_name = model_class._meta.table_name
            logger.info(f"开始保存{len(data_list)}条数据到表: {table_name}...")
            
            # 如果ignore_fields为None，则使用默认值
            if ignore_fields is None:
                ignore_fields = ["create_time", "update_time"]

            if replace:
                # 使用Peewee的on_conflict_replace进行批量插入和替换
                with self.common_db.atomic():
                    model_class.insert_many(data_list).on_conflict_replace().execute()
            else:
                # 获取模型的所有字段名，排除需要忽略的字段
                fields = [
                    field_name for field_name in model_class._meta.fields.keys()
                    if field_name not in ignore_fields
                ]

                # 构建INSERT ... ON DUPLICATE KEY UPDATE语句
                field_str = ', '.join(f"`{field}`" for field in fields)  # 添加反引号
                placeholders = '(' + ', '.join(['%s'] * len(fields)) + ')'
                value_placeholders = ', '.join([placeholders] * len(data_list))
                
                # 动态构建update_str，确保主键不被更新
                update_fields = [f"`{field}` = VALUES(`{field}`)" for field in fields if field != primary_key]
                update_str = ', '.join(update_fields)
                
                sql = f"""
                    INSERT INTO `{table_name}` ({field_str})
                    VALUES {value_placeholders}
                    ON DUPLICATE KEY UPDATE {update_str}
                """
                
                # 准备数据（展平为一维列表），安全获取字段值
                values = []
                for data in data_list:
                    values.extend(data.get(field, None) for field in fields)
                
                # 执行批量更新
                with self.common_db.atomic():
                    cursor = self.common_db.execute_sql(sql, values)
            
            logger.info(f"已保存{len(data_list)}条记录到表: {table_name}")
            
        except Exception as e:
            if replace:
                logger.error(f"批量保存（替换模式）数据到表 {table_name} 时发生错误: {str(e)}{traceback.format_exc()}")
            else:
                logger.error(f"保存数据到表 {table_name} 时发生错误: {str(e)}{traceback.format_exc()}")
                logger.error(f"sql: {sql}")
                logger.error(f"values: {values}")
            raise

    def process_and_save_queue(self, data_queue: queue.Queue, fetching_completed_event: threading.Event, model_class: Model, 
                           batch_size: int = 50, save_interval: int = 10, replace: bool = False) -> None:
        """从队列中获取并批量保存数据到数据库
        
        Args:
            data_queue: 数据队列
            fetching_completed_event: 数据获取完成事件
            model_class: Peewee模型类
            batch_size: 触发保存的数据条数，默认50条
            save_interval: 触发保存的时间间隔（秒），默认10秒
            replace: 是否使用replace模式进行批量保存，默认为False
        """
        data_list = []
        last_save_time = time.time()
        
        while not (fetching_completed_event.is_set() and data_queue.empty()):
            try:
                # 从队列获取数据，超时10秒
                data = data_queue.get(timeout=10)
                data_list.append(data)
                
                # 如果累积了足够的数据或者距离上次保存超过指定时间，就保存一次
                current_time = time.time()
                if len(data_list) >= batch_size or (current_time - last_save_time >= save_interval and data_list):
                    self.batch_save_to_db(data_list, model_class, replace=replace)
                    data_list = []
                    last_save_time = current_time
                    
            except queue.Empty:
                # 队列超时，如果有待保存的数据就保存
                if data_list:
                    self.batch_save_to_db(data_list, model_class, replace=replace)
                    data_list = []
                    last_save_time = time.time()
        
        # 保存剩余的数据
        if data_list:
            self.batch_save_to_db(data_list, model_class, replace=replace)

# 创建数据库管理器实例
db_manager = DatabaseManager()

# 复权因子表结构
class FutuRehab(Model):
    """复权因子表"""
    code = CharField()  # 股票代码
    ex_div_date = DateTimeField()  # 除权除息日

    # 前收价相关
    prev_close = DoubleField(null=True)  # 前一日收盘价
    exchange_prev_close = DoubleField(null=True)  # 交易所前收盘价

    # 拆合股相关
    split_base = DoubleField(null=True)  # 拆股分子
    split_ert = DoubleField(null=True)  # 拆股分母
    join_base = DoubleField(null=True)  # 合股分子
    join_ert = DoubleField(null=True)  # 合股分母
    split_ratio = DoubleField(null=True)  # 合股比例

    # 派息相关
    per_cash_div = DoubleField(null=True)  # 每股派现
    special_dividend = DoubleField(null=True)  # 特别股息

    # 送股相关
    bonus_base = DoubleField(null=True)  # 送股分子
    bonus_ert = DoubleField(null=True)  # 送股分母
    per_share_div_ratio = DoubleField(null=True)  # 送股比例

    # 转增股相关
    transfer_base = DoubleField(null=True)  # 转增股分子
    transfer_ert = DoubleField(null=True)  # 转增股分母
    per_share_trans_ratio = DoubleField(null=True)  # 转增股比例

    # 配股相关
    allot_base = DoubleField(null=True)  # 配股分子
    allot_ert = DoubleField(null=True)  # 配股分母
    allotment_ratio = DoubleField(null=True)  # 配股比例
    allotment_price = DoubleField(null=True)  # 配股价

    # 增发相关
    add_base = DoubleField(null=True)  # 增发股分子
    add_ert = DoubleField(null=True)  # 增发股分母
    stk_spo_ratio = DoubleField(null=True)  # 增发比例
    stk_spo_price = DoubleField(null=True)  # 增发价格

    # 新增复权因子字段
    share_factor = DoubleField(default=1.0)  # 股数调整因子
    div_factor = DoubleField(default=1.0)  # 股息调整因子
    combined_factor = DoubleField(default=1.0)  # 息数调整因子
    share_forward_cumprod = DoubleField(default=1.0)  # 后续股数总因子
    combined_forward_cumprod = DoubleField(default=1.0)  # 后续总因子

    # 复权因子
    forward_adj_factorA = DoubleField(null=True)  # 前复权因子A
    forward_adj_factorB = DoubleField(null=True)  # 前复权因子B
    backward_adj_factorA = DoubleField(null=True)  # 后复权因子A
    backward_adj_factorB = DoubleField(null=True)  # 后复权因子B

    # 时间戳字段
    create_date = DateTimeField(default=datetime.now)  # 创建时间
    update_date = DateTimeField(default=datetime.now)  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'futu_rehab'
        indexes = ((("code", "ex_div_date"), True),)  # 联合唯一索引

# 富途产品信息表
class FutuProduct(Model):
    """富途产品信息表"""
    id = AutoField()
    code = CharField()  # 股票代码
    lot_size = IntegerField(null=True)  # 每手股数
    stock_name = CharField(null=True)  # 股票名称 退市的以"(已退市)"结尾
    stock_owner = CharField(null=True)  # 所属正股
    stock_child_type = CharField(null=True)  # 子类型
    stock_type = CharField(null=True)  # 股票类型 STOCK ETF
    list_time = DateTimeField(null=True)  # 上市时间
    stock_id = CharField(null=True)  # 股票ID
    main_contract = BooleanField(default=False)  # 是否主连合约
    last_trade_time = DateTimeField(null=True)  # 最后交易时间
    created_time = DateTimeField()  # 创建时间
    rehab_known = BooleanField(default=True)  # 已收录复权因子

    class Meta:
        database = db_manager.common_db
        table_name = 'futu_product'
        indexes = (
            (('code', 'stock_id'), True),  # 联合唯一索引
        )


class FirstrateStock(Model):
    """FirstRate股票信息表"""
    id = AutoField()
    ticker = CharField()  # 股票代码
    company_name = CharField(null=True)  # 公司名称
    country = CharField(null=True)  # 国家
    state = CharField(null=True)  # 州/省
    exchange = CharField(null=True)  # 交易所
    sector = CharField(null=True)  # 行业分类
    industry = CharField(null=True)  # 子行业
    ipo_date = DateTimeField(null=True)  # 上市日期
    created_time = DateTimeField()  # 创建时间

    class Meta:
        database = db_manager.common_db
        table_name = 'firstrate_stock'
        indexes = (
            (('ticker', 'exchange'), True),  # 联合唯一索引
        )


class FirstrateRehab(Model):
    """FirstRate复权因子表"""
    id = AutoField()
    symbol = CharField()  # 股票代码
    ex_div_date = DateTimeField()  # 除权除息日期

    # 拆合股相关
    split_ratio = DoubleField(null=True)  # 拆合股比例（>1为拆股，<1为合股）

    # 派息相关
    dividend = DoubleField(null=True)  # 每股派息

    # 时间戳字段
    create_date = DateTimeField(default=datetime.now)  # 创建时间
    update_date = DateTimeField(default=datetime.now)  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'firstrate_rehab'
        indexes = ((("symbol", "ex_div_date"), True),)  # 联合唯一索引

# 定义IB产品数据模型
class IbProduct(Model):
    """IB产品信息表"""
    type = CharField(max_length=63, null=True)  # BOND, STK等
    symbol = CharField(null=True)  # BRK, BRKHEC等
    exchange_id = CharField(max_length=63, null=True)  # NYSE, TRADEWEB, IBDESK等
    local_symbol = CharField(null=True)  # BRK1.46510/23/3191918972等
    description = CharField(null=True)  # BRK 1.465 10/23/31等
    conid = BigIntegerField(null=True)  # 64位整数,范围约±9.2E18
    under_conid = BigIntegerField(null=True)  # 同上
    isin = CharField(null=True)  # CA08465W1005等,可能为null
    cusip = CharField(null=True)  # 08465W100等,可能为null
    currency = CharField(max_length=63, null=True)  # USD, JPY, EUR等
    country = CharField(max_length=63, null=True)  # US等
    is_prime_exch = CharField(max_length=1, null=True)  # T或null,改为CharField
    is_new_product = CharField(max_length=1, null=True)  # F或T
    assoc_entity_id = CharField(max_length=63, null=True)  # e1434383等,可能为null
    created_time = DateTimeField()  # 创建时间
    is_latest = BooleanField(default=False)  # 是否是最新记录
    group_id = BigIntegerField(null=True)  # 稳定的group编码

    class Meta:
        database = db_manager.common_db
        table_name = 'ib_product'
        indexes = (
            # 主要查询和去重字段的联合索引
            (('conid', 'symbol', 'local_symbol', 'exchange_id'), True),  # 唯一性约束
            (('symbol', 'exchange_id'), False),  # 常用查询组合
            (('type',), False),  # 单字段索引,用于按类型查询
            (('is_latest',), False),  # 最新记录索引
            (('group_id',), False),  # group_id索引
        )

class StableIdGroupMapping(Model):
    """稳定ID与group_id映射表"""
    stable_id = BigIntegerField(primary_key=True)  # 稳定ID (从美股品种信息.xlsx中读取)
    group_id = BigIntegerField(index=True)  # group_id (从IbProduct表中查询)
    priority = BooleanField(default=False)  # 是否在美股重点核查中
    create_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])  # 创建时间
    update_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'stable_id_group_mapping'

class StockAdjustmentUS(Model):
    """美股复权记录表"""
    id = AutoField()
    old_symbol = CharField()  # 原始股票代码
    new_symbol = CharField()  # 新股票代码
    exchange = CharField()  # 交易所
    old_price = DoubleField()  # 原价格
    new_price = DoubleField()  # 新价格
    price_multi = DoubleField()  # 价格倍数
    ex_date = DateTimeField()  # 除权日期
    create_date = DateTimeField(default=datetime.now)  # 创建时间
    update_date = DateTimeField(default=datetime.now)  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'stock_adjustment_us'

class IbContractDetail(Model):
    """IB合约详情表"""
    conid = BigIntegerField(primary_key=True)  # 合约ID作为主键
    
    # Contract字段
    sec_type = CharField(max_length=63, null=True)
    symbol = CharField(max_length=63, null=True)
    last_trade_date_or_contract_month = CharField(max_length=63, null=True)
    strike = DoubleField(null=True)
    right = CharField(max_length=63, null=True)
    multiplier = CharField(max_length=63, null=True)
    exchange = CharField(max_length=63, null=True)
    primary_exchange = CharField(max_length=63, null=True)
    currency = CharField(max_length=63, null=True)
    local_symbol = CharField(max_length=63, null=True)
    trading_class = CharField(max_length=63, null=True)
    include_expired = BooleanField(null=True)
    sec_id_type = CharField(max_length=63, null=True)
    sec_id = CharField(max_length=63, null=True)
    description = CharField(null=True)
    issuer_id = CharField(max_length=63, null=True)
    
    # ContractDetails字段
    market_name = CharField(max_length=63, null=True)
    min_tick = DoubleField(null=True)
    order_types = CharField(max_length=1024, null=True)  # 增加长度到1024
    valid_exchanges = CharField(max_length=512, null=True)  # 增加长度到512
    price_magnifier = IntegerField(null=True)
    under_conid = BigIntegerField(null=True)
    long_name = CharField(null=True)
    contract_month = CharField(max_length=63, null=True)
    industry = CharField(max_length=128, null=True)
    category = CharField(max_length=128, null=True)
    subcategory = CharField(max_length=128, null=True)
    time_zone_id = CharField(max_length=63, null=True)
    trading_hours = CharField(null=True)
    liquid_hours = CharField(null=True)
    ev_rule = CharField(max_length=63, null=True)
    ev_multiplier = IntegerField(null=True)
    md_size_multiplier = IntegerField(null=True)
    agg_group = IntegerField(null=True)
    under_symbol = CharField(max_length=63, null=True)
    under_sec_type = CharField(max_length=63, null=True)
    market_rule_ids = CharField(null=True)
    sec_id_list = CharField(null=True)
    real_expiration_date = CharField(max_length=63, null=True)
    last_trade_time = CharField(max_length=63, null=True)
    stock_type = CharField(max_length=63, null=True)
    min_size = DoubleField(null=True)
    size_increment = DoubleField(null=True)
    suggested_size_increment = DoubleField(null=True)
    cusip = CharField(max_length=63, null=True)
    ratings = CharField(max_length=63, null=True)
    desc_append = CharField(max_length=63, null=True)
    bond_type = CharField(max_length=63, null=True)
    coupon_type = CharField(max_length=63, null=True)
    callable = BooleanField(null=True)
    putable = BooleanField(null=True)
    coupon = DoubleField(null=True)
    convertible = BooleanField(null=True)
    maturity = CharField(max_length=63, null=True)
    issue_date = CharField(max_length=63, null=True)
    next_option_date = CharField(max_length=63, null=True)
    next_option_type = CharField(max_length=63, null=True)
    next_option_partial = BooleanField(null=True)
    notes = CharField(null=True)
    
    # 时间戳字段
    create_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])  # 创建时间
    update_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'ib_contract_detail'

# 定义合约时间信息表
class ContractTime(Model):
    """合约时间信息表，存储各类与合约相关的时间"""
    conid = BigIntegerField(primary_key=True)  # 合约ID，作为主键
    ib_head_time = DateTimeField(null=True)  # IB最早交易时间
    wind_ipo_date = DateTimeField(null=True) # Wind IPO日期
    yahoo_first_trade_date_milliseconds = DateTimeField(null=True) # Yahoo First Trade Date in Milliseconds
    futu_list_time = DateTimeField(null=True)  # 富途上市时间
    
    create_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])  # 创建时间
    update_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'contract_time'

# 定义IB基本面数据模型
class IbFundamentals(Model):
    """IB基本面数据表，存储ReportSnapshot解析后的数据"""
    conid = BigIntegerField(primary_key=True)  # 合约ID，作为主键
    
    # ========== 公司标识字段 ==========
    rep_no = CharField(max_length=63, null=True)  # 公司报告编号
    company_name = CharField(null=True)  # 公司全称
    irs_no = CharField(max_length=63, null=True)  # 美国税务局登记号
    cik_no = CharField(max_length=63, null=True)  # SEC中央索引键(用于EDGAR系统)
    organization_perm_id = CharField(max_length=63, null=True)  # 机构永久ID
    
    # ========== 指数成分股信息 ==========
    index_constituents = CharField(null=True)  # 所属指数列表，多个指数用分号分隔

    # ========== 公司基本信息 ==========
    shares_out = DoubleField(null=True)  # 总股本(单位：股)
    total_float = DoubleField(null=True)  # 流通股本(单位：股)
    shares_per_listing = DoubleField(null=True)  # ADR每份对应的普通股数量，非ADR为null
    
    # IB ReportSnapshot中的最近拆股信息
    most_recent_split_date = DateTimeField(null=True)  # 最近拆股日期
    most_recent_split_ratio = DoubleField(null=True)  # 最近拆股比例

    # ========== 财务比率数据 ==========
    # 价格相关指标
    nprice = DoubleField(null=True)  # 最新价格
    nhig = DoubleField(null=True)  # 52周最高价
    nlow = DoubleField(null=True)  # 52周最低价
    pdate = DateTimeField(null=True)  # 价格日期
    vol10davg = DoubleField(null=True)  # 10日平均成交量
    
    # 估值指标
    ev = DoubleField(null=True)  # 企业价值(Enterprise Value)
    mktcap = DoubleField(null=True)  # 市值(Market Capitalization)
    
    # 利润表指标(TTM: 最近12个月)
    ttmrev = DoubleField(null=True)  # 总收入
    ttmebitd = DoubleField(null=True)  # 息税折旧前利润
    ttmniac = DoubleField(null=True)  # 归属于普通股的净利润
    ttmepsxclx = DoubleField(null=True)  # 每股收益(排除特殊项目)
    ttmrevps = DoubleField(null=True)  # 每股收入
    
    # 资产负债表指标
    qbvps = DoubleField(null=True)  # 每股账面价值(最新季度)
    qcshps = DoubleField(null=True)  # 每股现金流(最新季度)
    
    # 现金流与分红
    ttmcfshr = DoubleField(null=True)  # 每股现金流(TTM)
    ttmdivshr = DoubleField(null=True)  # 每股分红(TTM)
    
    # 盈利能力比率
    ttmgrosmgn = DoubleField(null=True)  # 毛利率(%)
    ttmroepct = DoubleField(null=True)  # 净资产收益率(%)
    ttmpr2rev = DoubleField(null=True)  # 市销率(Price to Sales)
    
    # 估值比率
    peexclxor = DoubleField(null=True)  # 市盈率(排除特殊项目)
    price2bk = DoubleField(null=True)  # 市净率
    
    # 其他
    employees = DoubleField(null=True)  # 员工人数

    create_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])  # 创建时间
    update_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'ib_fundamentals'

# 定义Yahoo财经信息数据模型
class YahooInfo(Model):
    """Yahoo财经信息表"""
    isin = CharField(primary_key=True, max_length=63)  # ISIN作为主键
    conid = BigIntegerField(null=True)  # 关联的IB合约ID
    
    # 基本信息
    symbol = CharField(null=True, max_length=32)  # 股票代码
    short_name = CharField(null=True, max_length=128)  # 简称
    long_name = CharField(null=True, max_length=128)  # 长称
    display_name = CharField(null=True, max_length=128)  # 显示名称
    quote_type = CharField(null=True, max_length=32)  # 报价类型
    currency = CharField(null=True, max_length=16)  # 交易货币
    exchange = CharField(null=True, max_length=32)  # 交易所
    market = CharField(null=True, max_length=32)  # 市场
    first_trade_date_milliseconds = DateTimeField(null=True)  # 首次交易日期
    
    # 公司信息
    sector = CharField(null=True, max_length=64)  # 行业部门
    industry = CharField(null=True, max_length=64)  # 行业
    full_time_employees = IntegerField(null=True)  # 全职员工数
    website = CharField(null=True)  # 公司网站
    ir_website = CharField(null=True)  # IR网站地址
    
    # 地址信息
    address1 = CharField(null=True)  # 地址
    city = CharField(null=True, max_length=64)  # 城市
    state = CharField(null=True, max_length=32)  # 州/省
    zip = CharField(null=True, max_length=32)  # 邮编
    country = CharField(null=True, max_length=64)  # 国家
    phone = CharField(null=True, max_length=32)  # 电话
    
    # 价格和估值指标
    market_cap = BigIntegerField(null=True)  # 市值
    enterprise_value = BigIntegerField(null=True)  # 企业价值
    regular_market_previous_close = DoubleField(null=True)  # 前收盘价
    target_high_price = DoubleField(null=True)  # 目标最高价
    target_low_price = DoubleField(null=True)  # 目标最低价
    target_mean_price = DoubleField(null=True)  # 目标均价
    target_median_price = DoubleField(null=True)  # 目标中位价
    trailing_pe = DoubleField(null=True)  # 市盈率(TTM)
    forward_pe = DoubleField(null=True)  # 预期市盈率
    trailing_peg_ratio = DoubleField(null=True)  # 市盈率增长比率(TTM)
    beta = DoubleField(null=True)  # 贝塔系数
    price_to_book = DoubleField(null=True)  # 市净率
    enterprise_to_revenue = DoubleField(null=True)  # 企业价值/收入比
    enterprise_to_ebitda = DoubleField(null=True)  # 企业价值/EBITDA比
    
    # 分析师评级信息
    recommendation_key = CharField(null=True, max_length=32)  # 推荐评级关键字
    number_of_analyst_opinions = IntegerField(null=True)  # 分析师意见数量
    
    # 财报时间
    earnings_timestamp = DateTimeField(null=True)  # 财报时间戳
    last_fiscal_year_end = DateTimeField(null=True)  # 上一财年结束时间
    next_fiscal_year_end = DateTimeField(null=True)  # 下一财年结束时间
    
    # 股本结构
    shares_outstanding = BigIntegerField(null=True)  # 总股本
    float_shares = BigIntegerField(null=True)  # 流通股本
    implied_shares_outstanding = BigIntegerField(null=True)  # 推定总股本
    held_percent_insiders = DoubleField(null=True)  # 内部人持股比例
    held_percent_institutions = DoubleField(null=True)  # 机构持股比例
    
    # 空头信息
    shares_short = BigIntegerField(null=True)  # 空头股数
    shares_short_prior_month = BigIntegerField(null=True)  # 上月空头股数
    shares_short_previous_month_date = DateTimeField(null=True)  # 上月空头日期
    date_short_interest = DateTimeField(null=True)  # 空头利息日期
    shares_percent_shares_out = DoubleField(null=True)  # 空头占总股本比例
    short_ratio = DoubleField(null=True)  # 空头比例
    short_percent_of_float = DoubleField(null=True)  # 空头占流通股本比例
    
    # 财务数据
    total_revenue = BigIntegerField(null=True)  # 总收入
    gross_profits = BigIntegerField(null=True)  # 毛利
    total_cash = BigIntegerField(null=True)  # 总现金
    total_debt = BigIntegerField(null=True)  # 总债务
    operating_cash_flow = BigIntegerField(null=True)  # 经营现金流
    free_cash_flow = BigIntegerField(null=True)  # 自由现金流
    
    # 财务比率
    profit_margins = DoubleField(null=True)  # 利润率
    operating_margins = DoubleField(null=True)  # 营业利润率
    gross_margins = DoubleField(null=True)  # 毛利率
    ebitda_margins = DoubleField(null=True)  # EBITDA利润率
    return_on_equity = DoubleField(null=True)  # 股本回报率
    return_on_assets = DoubleField(null=True)  # 资产回报率
    current_ratio = DoubleField(null=True)  # 流动比率
    quick_ratio = DoubleField(null=True)  # 速动比率
    debt_to_equity = DoubleField(null=True)  # 资产负债率
    earnings_growth = DoubleField(null=True)  # 盈利增长率
    revenue_growth = DoubleField(null=True)  # 收入增长率
    
    # 股息信息
    dividend_rate = DoubleField(null=True)  # 股息率
    dividend_yield = DoubleField(null=True)  # 股息收益率
    payout_ratio = DoubleField(null=True)  # 派息比率
    ex_dividend_date = DateTimeField(null=True)  # 除息日
    last_dividend_date = DateTimeField(null=True)  # 最后派息日
    dividend_date = DateTimeField(null=True)  # 派息日
    
    # 拆股信息
    last_split_factor = CharField(null=True, max_length=32)  # 最后一次拆股比例
    last_split_date = DateTimeField(null=True)  # 最后一次拆股日期
    
    # 时间戳字段
    create_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])  # 创建时间
    update_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'yahoo_info'

class WindStock(Model):
    """Wind股票信息表"""
    windcodes = CharField(primary_key=True, max_length=20)  # 股票代码
    comp_name = CharField(max_length=100, null=True)  # 公司中文名称
    ipo_date = DateTimeField(null=True)  # 上市日期
    wicsname2024 = CharField(max_length=100, null=True)  # 行业分类
    sec_status = CharField(max_length=100, null=True)  # 证券状态（L 上市，N 是新证券，未上市，D 是退市）
    exch_eng = CharField(max_length=100, null=True)  # 交易所英文名
    isin_code = CharField(max_length=100, null=True)  # ISIN代码
    pe = DoubleField(null=True)  # 市盈率
    pb = DoubleField(null=True)  # 市净率
    turn = DoubleField(null=True)  # 换手率
    ev3 = DoubleField(null=True)  # 企业价值倍数
    total_shares = DoubleField(null=True)  # 总股本
    wgsd_sales = DoubleField(null=True)  # 营业总收入
    wgsd_net_inc_cf = DoubleField(null=True)  # 净利润
    wgsd_assets = DoubleField(null=True)  # 总资产
    wgsd_liabs = DoubleField(null=True)  # 总负债
    dividendyield2 = DoubleField(null=True)  # 股息率
    susp_days = IntegerField(null=True)  # 停牌天数
    share_issuing_mkt = DoubleField(null=True) # 流通股本（含限售股）

    class Meta:
        database = db_manager.common_db
        table_name = 'wind_stock'

class IbShortStock(Model):
    """IB做空数据表"""
    conid = BigIntegerField()  # 合约ID，作为主键
    datetime = DateTimeField(default=datetime.now()) # 时间
    isin = CharField(max_length=63, null=True) # ISIN
    symbol = CharField(max_length=63, null=True) # 股票代码
    currency = CharField(max_length=63, null=True) # 货币
    name = CharField(max_length=100, null=True) # 股票名称
    rebate_rate = DoubleField(null=True) # 现金利率-融券费率（%）
    fee_rate = DoubleField(null=True) # 融券费率（%）
    available = CharField(max_length=100,null=True) # 融券池
    figi = CharField(max_length=63, null=True) # FIGI

    class Meta:
        database = db_manager.common_db
        table_name = 'ib_shortstock'
        indexes = (
            (('conid', 'datetime'), True),
        )

class YahooQuote(Model):
    """Yahoo财经盘口信息表"""
    isin = CharField(max_length=63)  # ISIN作为主键的一部分
    conid = BigIntegerField(null=True)  # 关联的IB合约ID
    datetime = DateTimeField()  # 记录时间
    ask = DoubleField(null=True)  # 卖一价
    ask_size = DoubleField(null=True)  # 卖一量
    bid = DoubleField(null=True)  # 买一价
    bid_size = DoubleField(null=True)  # 买一量

    class Meta:
        database = db_manager.common_db
        table_name = 'yahoo_quote'
        indexes = (
            (('isin', 'datetime'), True),  # 联合唯一索引
        )

class IbQuote(Model):
    """IB盘口信息表"""
    conid = BigIntegerField()  # 关联的IB合约ID
    datetime = DateTimeField()  # 记录时间
    ask = DoubleField(null=True)  # 卖一价
    ask_size = DoubleField(null=True)  # 卖一量
    bid = DoubleField(null=True)  # 买一价
    bid_size = DoubleField(null=True)  # 买一量

    class Meta:
        database = db_manager.common_db
        table_name = 'ib_quote'
        indexes = (
            (('conid', 'datetime'), True),  # 联合唯一索引
        )

class TipranksRehab(Model):
    """Tipranks复权因子表"""
    id = AutoField()
    symbol = CharField()  # 股票代码
    description = CharField(null=True)  # 股票描述
    ex_div_date = DateTimeField()  # 除权除息日期

    # 拆合股相关
    split_ratio = DoubleField(null=True)  # 拆合股比例（>1为拆股，<1为合股）

    # 派息相关
    dividend = DoubleField(null=True)  # 每股派息

    # 时间戳字段
    create_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])  # 创建时间
    update_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'tipranks_rehab'
        indexes = ((("symbol", "ex_div_date"), True),)  # 联合唯一索引

class PolygonRehab(Model):
    """Polygon复权因子表"""
    id = AutoField()
    symbol = CharField()  # 股票代码
    ex_div_date = DateTimeField()  # 除权除息日期
    # 派息相关
    dividend = DoubleField(null=True)  # 每股派息
    # 拆合股相关
    split_from = DoubleField(null=True) #起始股数
    split_to = DoubleField(null=True) #目标股数
    split_ratio = DoubleField(null=True)  # 拆合股比例（>1为拆股，<1为合股）


    # 时间戳字段
    create_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])  # 创建时间
    update_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'polygon_rehab'
        indexes = ((("symbol", "ex_div_date"), True),)  # 联合唯一索引


# cn.investing.com分红+拆合股数据模型
class InvestingRehabOriginal(Model):
    ex_div_date = DateTimeField()  # 除权除息日
    region = CharField()  # 交易所地区
    symbol = CharField()  # 股票代码
    description = CharField()  # 公司名称

    # IBKR相关(不作索引, 数据库目前只有美股数据)
    ib_symbol = CharField(null=True)
    conid = BigIntegerField(null=True)

    # 拆合股相关
    split_base = DoubleField(null=True)  # 拆股分子
    split_ert = DoubleField(null=True)  # 拆股分母
    join_base = DoubleField(null=True)  # 合股分子
    join_ert = DoubleField(null=True)  # 合股分母
    base = DoubleField(null=True)
    ert = DoubleField(null=True)
    split_ratio = DoubleField(null=True)  # 合股比例

    # 派息相关
    div_amount = DoubleField(null=True)  # 分红金额
    div_type = CharField()  # 分红类型 (1个月/季度/半年度/年度/ltm/中期/末期/其他/分红)
    pmt_date = DateTimeField()
    div_yield = DoubleField(null=True)

    # 时间戳字段
    create_date = DateTimeField(default=datetime.now)  # 创建时间
    update_date = DateTimeField(default=datetime.now)  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'investing_rehab_original'
        indexes = (
            (("symbol", "region", "description", "ex_div_date"), False),
        )  # 同一公司可能同一天分红多次/分红+拆合股/多次拆合股(?),故索引不唯一


# cn.investing.com分红+拆合股数据模型
class InvestingRehabMerged(Model):
    ex_div_date = DateTimeField()  # 除权除息日
    region = CharField()  # 交易所地区
    symbol = CharField()  # 股票代码
    description = CharField()  # 公司名称

    # IBKR相关(不作索引, 数据库目前只有美股数据)
    ib_symbol = CharField(null=True)
    conid = BigIntegerField(null=True)

    # 拆合股相关
    split_base = DoubleField(null=True)  # 拆股分子
    split_ert = DoubleField(null=True)  # 拆股分母
    join_base = DoubleField(null=True)  # 合股分子
    join_ert = DoubleField(null=True)  # 合股分母
    base = DoubleField(null=True)
    ert = DoubleField(null=True)
    split_ratio = DoubleField(null=True)  # 合股比例

    # 派息相关
    div_amount = DoubleField(null=True)  # 分红金额
    div_type = CharField()  # 分红类型 (1个月/季度/半年度/年度/ltm/中期/末期/其他/分红)
    pmt_date = DateTimeField()
    div_yield = DoubleField(null=True)

    # 公司行动次数
    action_count = IntegerField()

    # 时间戳字段
    create_date = DateTimeField(default=datetime.now)  # 创建时间
    update_date = DateTimeField(default=datetime.now)  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'investing_rehab_merged'
        indexes = (
            (("symbol", "region", "description", "ex_div_date"), True),
        )  # 同一公司同一天分红多次/分红+拆合股/多次拆合股(?)合并为一行,故逻辑上索引唯一

class StockinvestRehab(Model):
    """Stockinvest复权因子表"""
    id = AutoField()
    symbol = CharField()  # 股票代码
    name =  CharField() #gongs
    ex_div_date = DateTimeField(null=True)  # 除权除息日期
    # 派息相关

    dividend = DoubleField(null=True)  # 每股派息

    # 时间戳字段
    create_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])  # 创建时间
    update_time = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])  # 更新时间

    class Meta:
        database = db_manager.common_db
        table_name = 'stockinvest_rehab'
        indexes = ((("symbol", "ex_div_date"), True),)  # 联合唯一索引