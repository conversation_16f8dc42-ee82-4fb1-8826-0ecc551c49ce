from typing import Dict, List, Tuple
import typer
from loguru import logger
# logger.remove()
import os
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志

import sys, os
# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from utils.database_manager import IbProduct, StableIdGroupMapping
from vnpy.trader.setting import SETTINGS
from utils.mysql_database import create_mysql_database
from vnpy.trader.database import BaseDatabase
from utils.wecom_alert import report_we_alert, WecomAlertManager
from vnpy.trader.utility import get_file_path
import pandas as pd
import shutil
from utils.mixin import NA_VALUES
manager = WecomAlertManager(key="b54436b7-2384-4b54-863f-caebc34309a4", use_markdown_v2=True)
manager.start()
app = typer.Typer()

def get_conid_mapping(old_conids: set) -> Tuple[Dict[int, dict], Dict[int, dict]]:
    """获取旧conid到新conid的映射，以及需要警告的映射
    
    Args:
        old_conids: 旧conid集合
    
    Returns:
        Tuple[Dict[int, dict], Dict[int, dict]]: 
            - 第一个字典: {old_conid: {'new_conid': new_conid, 'old_symbol': old_symbol, 'new_symbol': new_symbol, 'stable_id': stable_id, 'priority': priority}}
            - 第二个字典: {old_conid: {'new_conid': new_conid, 'old_symbol': old_symbol, 'new_symbol': new_symbol, 'stable_id': stable_id, 'priority': priority}}
            分别代表有效的映射和需要警告的映射
    """
    # 1. 获取所有需要的数据
    # 查询old_conids对应的group_id和symbol
    old_products = (IbProduct
                   .select(IbProduct.conid, IbProduct.group_id, IbProduct.symbol)
                   .where(IbProduct.conid.in_(old_conids)))
    
    # 获取所有最新的conid、group_id和symbol
    latest_products = (IbProduct
                      .select(IbProduct.conid, IbProduct.group_id, IbProduct.symbol)
                      .where(IbProduct.is_latest == True))
    
    # 获取stable_id和priority信息
    stable_mappings = {}
    for mapping in StableIdGroupMapping.select():
        stable_mappings[mapping.group_id] = {
            'stable_id': mapping.stable_id,
            'priority': mapping.priority
        }
    
    # 2. 构建本地缓存
    latest_conids = {p.conid for p in latest_products}
    conid_to_group_id = {p.conid: {'group_id': p.group_id, 'symbol': p.symbol} for p in old_products if p.group_id}
    group_id_to_new_product = {p.group_id: {'conid': p.conid, 'symbol': p.symbol} 
                               for p in latest_products if p.group_id}
    
    # 3. 在本地缓存中处理映射关系
    conid_mappings = {}
    warning_mappings = {}
    
    for old_conid in old_conids:
        if old_conid not in latest_conids:
            old_info = conid_to_group_id.get(old_conid)
            if old_info:
                group_id = old_info['group_id']
                new_info = group_id_to_new_product.get(group_id)
                stable_info = stable_mappings.get(group_id, {'stable_id': None, 'priority': False})
                
                if new_info:
                    new_conid = new_info['conid']
                    mapping_info = {
                        'new_conid': new_conid,
                        'old_symbol': old_info['symbol'],
                        'new_symbol': new_info['symbol'],
                        'stable_id': stable_info['stable_id'],
                        'priority': stable_info['priority']
                    }
                    # 检查new_conid是否在old_conids中,如果在说明链式判断有误
                    if new_conid in old_conids:
                        warning_mappings[old_conid] = mapping_info
                    else:
                        conid_mappings[old_conid] = mapping_info

    return conid_mappings, warning_mappings

def format_conid_mappings_as_table(mappings: Dict[int, dict], title: str) -> str:
    """将conid映射信息格式化为markdown表格
    
    Args:
        mappings: conid映射字典
        title: 表格标题
        
    Returns:
        str: markdown格式的表格字符串
    """
    if not mappings:
        return f"**{title}**: 无需更新的conid"
    
    # 构建表格数据
    table_data = []
    for old_conid, mapping_info in mappings.items():
        priority_text = "🔴" if mapping_info['priority'] else "⚪"
        stable_id_text = str(mapping_info['stable_id']) if mapping_info['stable_id'] else "-"
        
        table_data.append({
            '旧ConID': old_conid,
            '旧Symbol': mapping_info['old_symbol'],
            '新ConID': mapping_info['new_conid'], 
            '新Symbol': mapping_info['new_symbol'],
            'StableID': stable_id_text,
            '重点': priority_text
        })
    
    # 转换为DataFrame并生成markdown表格
    df = pd.DataFrame(table_data)
    markdown_table = df.to_markdown(index=False, tablefmt='github')
    
    return f"**{title}** ({len(mappings)}个):\n\n{markdown_table}"

def update_database_conids(database: BaseDatabase, conid_mappings: Dict[int, dict]):
    """更新数据库中的conid
    
    Args:
        database: 数据库实例
        conid_mappings: conid映射字典，格式为 {old_conid: {'new_conid': new_conid, 'old_symbol': old_symbol, 'new_symbol': new_symbol}}
    """
    with database.db.atomic():
        for old_conid, mapping_info in conid_mappings.items():
            new_conid = mapping_info['new_conid']
            # 更新DbBarOverview表
            database.db.execute_sql(
                "UPDATE dbbaroverview SET symbol = %s WHERE symbol = %s",
                (str(new_conid), str(old_conid))
            )
            database.db.execute_sql(
                "UPDATE dbbaroverview SET symbol = REPLACE(symbol, %s, %s) WHERE symbol LIKE %s",
                (f"{old_conid}_", f"{new_conid}_", f"{old_conid}_%")
            )
            
            # 更新DbBarData表
            database.db.execute_sql(
                "UPDATE dbbardata SET symbol = %s WHERE symbol = %s",
                (str(new_conid), str(old_conid))
            )
            database.db.execute_sql(
                "UPDATE dbbardata SET symbol = REPLACE(symbol, %s, %s) WHERE symbol LIKE %s",
                (f"{old_conid}_", f"{new_conid}_", f"{old_conid}_%")
            )

def update_scanner_csv_conids(csv_file_path: str, conid_mappings: Dict[int, dict]) -> Tuple[int, int]:
    """更新扫描器CSV文件中的conId和symbol
    
    Args:
        csv_file_path: CSV文件路径
        conid_mappings: conid映射字典，格式为 {old_conid: {'new_conid': new_conid, 'old_symbol': old_symbol, 'new_symbol': new_symbol}}
    
    Returns:
        Tuple[int, int]: (更新的行数, 总行数)
    """
    if not os.path.exists(csv_file_path):
        logger.error(f"CSV文件不存在: {csv_file_path}")
        return 0, 0
    
    # 备份原文件
    backup_file = f"{csv_file_path}.backup"
    shutil.copy2(csv_file_path, backup_file)
    logger.info(f"已备份原文件到: {backup_file}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file_path, na_values=NA_VALUES, keep_default_na=False)
    total_rows = len(df)
    updated_rows = 0
    
    # 检查必要的列是否存在
    if 'conId' not in df.columns or 'symbol' not in df.columns:
        logger.error("CSV文件缺少必要的列: conId 或 symbol")
        return 0, total_rows
    
    # 更新conId和symbol
    for index, row in df.iterrows():
        old_conid = row['conId']
        if pd.notna(old_conid) and int(old_conid) in conid_mappings:
            mapping_info = conid_mappings[int(old_conid)]
            new_conid = mapping_info['new_conid']
            new_symbol = mapping_info['new_symbol']
            
            # 更新conId和symbol
            df.at[index, 'conId'] = new_conid
            df.at[index, 'symbol'] = new_symbol
            updated_rows += 1
            
            logger.info(f"更新第{index+1}行: conId {old_conid} -> {new_conid}, symbol {row['symbol']} -> {new_symbol}")
    
    # 保存更新后的文件
    df.to_csv(csv_file_path, index=False)
    logger.info(f"已保存更新后的CSV文件: {csv_file_path}")
    
    return updated_rows, total_rows

@app.command()
def update(
    database_name: str = typer.Option(
        None,
        "--database",
        "-d",
        help="数据库名称"
    ),
    csv_file: str = typer.Option(
        "scanner_unique_stk_us_all.csv",
        "--csv-file",
        "-f",
        help="扫描器CSV文件名"
    ),
    update_csv: bool = typer.Option(
        False,
        "--update-csv",
        "-csv",
        help="是否更新CSV文件。不传入时只更新数据库，传入时只更新CSV文件"
    )
):
    """更新数据库或扫描器CSV文件中的conid"""
    try:
        if update_csv:
            # 只更新扫描器CSV文件
            logger.info("开始更新扫描器CSV文件...")
            csv_file_path = get_file_path(csv_file)
            
            if not os.path.exists(csv_file_path):
                logger.error(f"CSV文件不存在: {csv_file_path}")
                return
            
            # 读取CSV文件获取所有conId
            df = pd.read_csv(csv_file_path, na_values=NA_VALUES, keep_default_na=False)
            
            if 'conId' not in df.columns:
                logger.error("CSV文件缺少conId列")
                return
            
            # 获取所有conId
            csv_old_conids = set()
            for conid in df['conId']:
                if pd.notna(conid):
                    try:
                        csv_old_conids.add(int(conid))
                    except ValueError:
                        continue
            
            logger.info(f"从CSV文件中获取到 {len(csv_old_conids)} 个conId")
            
            # 获取CSV的conid映射
            csv_conid_mappings, csv_warning_mappings = get_conid_mapping(csv_old_conids)
            
            if csv_warning_mappings:
                logger.warning(f"CSV文件中发现 {len(csv_warning_mappings)} 个链式判断错误的映射关系，这些映射将被跳过:")
                for old_conid, mapping_info in csv_warning_mappings.items():
                    logger.warning(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                                 f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
            
            if csv_conid_mappings:
                logger.info(f"CSV文件中找到 {len(csv_conid_mappings)} 个需要更新的conId:")
                for old_conid, mapping_info in csv_conid_mappings.items():
                    logger.info(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                               f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
                
                updated_rows, total_rows = update_scanner_csv_conids(csv_file_path, csv_conid_mappings)
                logger.info(f"CSV文件更新完成: 更新了 {updated_rows}/{total_rows} 行")
            else:
                logger.info("CSV文件中没有需要更新的conId")
            
            # 发送企业微信通知
            message_parts = []
            message_parts.append("# update_conid CSV更新运行完成")
            message_parts.append(f"**CSV文件**: {csv_file}")
            
            if csv_conid_mappings:
                message_parts.append(f"**CSV文件更新结果**: 更新了 {updated_rows}/{total_rows} 行")
                csv_table = format_conid_mappings_as_table(csv_conid_mappings, "自动更新的ConID")
                message_parts.append(csv_table)
            else:
                message_parts.append("**CSV文件**: 没有需要更新的conId")
            
            if csv_warning_mappings:
                warning_table = format_conid_mappings_as_table(csv_warning_mappings, "⚠️ 需要人工核对的ConID")
                message_parts.append(warning_table)
            else:
                message_parts.append("**人工核对**: 没有需要人工核对的conId")
                
            message = "\n\n".join(message_parts)
            manager.add_message(message)
            manager.stop()
            return

        # 只更新数据库
        # 检查当前是否为交易日
        from datetime import datetime
        from vnpy.trader.utility import ZoneInfo
        import pandas_market_calendars as mcal
        
        ET_TZ = ZoneInfo("America/New_York")  # 美东时间
        nyse = mcal.get_calendar('NYSE')
        now = datetime.now(ET_TZ)
        today = now.date()
        schedule = nyse.schedule(start_date=today, end_date=today)
        # if schedule.empty:
        #     logger.warning(f"当前日期 {today} 不是交易日，程序退出")
        #     return
            
        # 创建数据库连接
        settings = SETTINGS.copy()
        if database_name:
            settings['database.database'] = database_name
            logger.info(f"使用指定的数据库: {database_name}")
        else:
            logger.info(f"使用默认数据库: {settings['database.database']}")
            
        database, _, _, _, _ = create_mysql_database(settings)
            
        # 1. 从数据库overview中获取所有旧conid
        old_conids = set()
        for overview in database.get_bar_overview():
            if '_' not in overview.symbol:
                try:
                    old_conids.add(int(overview.symbol))
                except ValueError:
                    continue
        
        # 2. 获取conid映射和警告映射
        logger.info("正在获取conid映射关系...")
        conid_mappings, warning_mappings = get_conid_mapping(old_conids)
        
        # 3. 如果有警告映射，输出警告信息
        if warning_mappings:
            logger.warning(f"\n发现 {len(warning_mappings)} 个链式判断错误的映射关系，这些映射将被跳过:")
            for old_conid, mapping_info in warning_mappings.items():
                logger.warning(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                             f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
            logger.warning("请人工核查这些映射关系")
        
        if conid_mappings:
            logger.info(f"找到 {len(conid_mappings)} 个需要更新的conid:")
            for old_conid, mapping_info in conid_mappings.items():
                logger.info(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                           f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
            
        # 4. 更新数据库
        logger.info("正在更新数据库...")
        update_database_conids(database, conid_mappings)
        logger.info("数据库更新完成")

        # 5. 发送企业微信通知
        host = settings['database.host']
        port = settings['database.port']
        database_name = settings['database.database']
        
        message_parts = []
        message_parts.append("# update_conid 数据库更新运行完成")
        message_parts.append(f"**数据库**: {host}:{port}/{database_name}")
        
        if conid_mappings:
            db_table = format_conid_mappings_as_table(conid_mappings, "已自动更新的ConID")
            message_parts.append(db_table)
        else:
            message_parts.append("**数据库更新**: 没有需要自动更新的conid")
        
        if warning_mappings:
            warning_table = format_conid_mappings_as_table(warning_mappings, "⚠️ 需要人工核对的ConID")
            message_parts.append(warning_table)
        else:
            message_parts.append("**人工核对**: 没有需要人工核对的conid")
            
        message = "\n\n".join(message_parts)
        manager.add_message(message)
        manager.stop()  # 停止消息处理线程
        
    except Exception as e:
        logger.error(f"更新过程中出错: {str(e)}")
        raise



if __name__ == "__main__":
    app()
