Positions  职位Copy Location  复制位置
A limitation of the function IBApi.EClient.reqAccountUpdates is that it can only be used with a single account at a time. To create a subscription for position updates from multiple accounts, the function IBApi.EClient.reqPositions is available.
IBApi.EClient.reqAccountUpdates 函数的一个限制是它一次只能用于一个账户。要创建多个账户的持仓更新订阅，可以使用 IBApi.EClient.reqPositions 函数。

Note: The reqPositions function is not available in Introducing Broker or Financial Advisor master accounts that have very large numbers of subaccounts (> 50) to optimize the performance of TWS/IB Gateway. Instead the function reqPositionsMulti can be used to subscribe to updates from individual subaccounts. Also not available with IBroker accounts configured for on-demand account lookup.
注意： 为了优化 TWS/IB 网关的性能，reqPositions 函数不适用于包含大量子账户（> 50）的介绍经纪商或财务顾问主账户。您可以使用 reqPositionsMulti 函数订阅各个子账户的更新。对于配置为按需账户查询的 IBroker 账户，该函数同样不适用。

After initially invoking reqPositions, information about all positions in all associated accounts will be returned, followed by the IBApi::EWrapper::positionEnd callback. Thereafter, when a position has changed an update will be returned to the IBApi::EWrapper::position function. To cancel a reqPositions subscription, invoke IBApi::EClient::cancelPositions.
首次调用 reqPositions 后，将返回所有关联账户中所有仓位的信息，然后执行 IBApi::EWrapper::positionEnd 回调。此后，当仓位发生变化时，更新将返回至 IBApi::EWrapper::position 函数。如需取消 reqPositions 订阅，请调用 IBApi::EClient::cancelPositions。

Request Positions  申请职位Copy Location  复制位置
EClient.reqPositions()
Subscribes to position updates for all accessible accounts. All positions sent initially, and then only updates as positions change.
订阅所有可访问账户的仓位更新。初始发送所有仓位信息，之后仅在仓位发生变化时更新。

Python
Java
C++
C#
VB.NET
self.reqPositions()


Code example:  代码示例：

from ibapi.client import *
from ibapi.wrapper import *
import threading
import time
class TradingApp(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self,self)
    def position(self, account: str, contract: Contract, position: Decimal, avgCost: float):
        print("Position.", "Account:", account, "Contract:", contract, "Position:", position, "Avg cost:", avgCost)

    def positionEnd(self):
       print("PositionEnd")

def websocket_con():
    app.run()

app = TradingApp()
app.connect("127.0.0.1", 7496, clientId=1)
con_thread = threading.Thread(target=websocket_con, daemon=True)
con_thread.start()
time.sleep(1)
app.reqPositions()
time.sleep(1)


Receive Positions  接收职位Copy Location  复制位置
EWrapper.position(  EWrapper.位置(
account: String. The account holding the position.
account： 字符串。持仓账户。

contract: Contract. The position’s Contract
contract： 合同。该职位的合同

pos: decimal. The number of positions held. avgCost the average cost of the position.
pos： 小数。持仓数量。avgCost 持仓平均成本。

avgCost: double. The total average cost of all trades for the currently held position.
avgCost： double。当前持仓所有交易的平均成本总和。
)

Provides the portfolio’s open positions. After the initial callback (only) of all positions, the IBApi.EWrapper.positionEnd function will be triggered.
提供投资组合的未平仓仓位。所有仓位的初始回调（仅限）结束后，将触发 IBApi.EWrapper.positionEnd 函数。

For futures, the exchange field will not be populated in the position callback as some futures trade on multiple exchanges
对于期货，由于一些期货在多个交易所交易，因此在持仓回调中不会填充交易所字段

Python
Java
C++
C#
VB.NET
def position(self, account: str, contract: Contract, position: Decimal, avgCost: float):
  print("Position.", "Account:", account, "Contract:", contract, "Position:", position, "Avg cost:", avgCost)


Ewrapper.positionEnd()
Indicates all the positions have been transmitted. Only returned after the initial callback of EWrapper.position.
表示所有位置信息均已传输。仅在 EWrapper.position 初始回调后返回。

Python
Java
C++
C#
VB.NET
def positionEnd(self):
  print("PositionEnd")


Cancel Positions Request  取消持仓请求Copy Location  复制位置
EClient.cancelPositions()
EClient.取消仓位()
Cancels a previous position subscription request made with EClient.reqPositions().
取消之前通过 EClient.reqPositions() 发出的仓位订阅请求。

Python
Java
C++
C#
VB.NET
self.cancelPositions()