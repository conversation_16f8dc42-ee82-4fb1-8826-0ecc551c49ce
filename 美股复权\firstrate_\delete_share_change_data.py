"""
删除有股数变动的标的数据脚本
从CSV文件中读取share_factor不为1的conid，然后删除对应的数据库记录
"""
import pandas as pd
import sys
import os
from datetime import datetime

# 添加路径以导入utils模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.mysql_database import create_mysql_database
from vnpy.trader.utility import load_json
from loguru import logger

class ShareChangeDataDeleter:
    """股数变动数据删除器"""
    
    def __init__(self, csv_file: str, target_db_name: str = "vnpy_stk_us_ib_m_2206_250814"):
        """
        初始化
        Args:
            csv_file: CSV文件路径
            target_db_name: 目标数据库名称
        """
        self.csv_file = csv_file
        self.target_db_name = target_db_name
        
        # 加载数据库配置并创建目标数据库连接
        db_setting = load_json("vt_setting.json")
        db_setting["database.database"] = target_db_name
        self.target_db, self.DbBarData, self.DbTickData, self.DbBarOverview, self.DbTickOverview = create_mysql_database(db_setting)
        
        logger.info(f"初始化数据删除器，目标数据库: {target_db_name}")
        logger.info(f"CSV文件: {csv_file}")
    
    def load_share_change_conids(self):
        """从CSV文件加载有股数变动的conid集合"""
        logger.info("从CSV文件加载有股数变动的conid...")
        
        # 读取CSV文件
        df = pd.read_csv(self.csv_file, encoding='utf-8-sig')
        
        # 筛选share_factor不为1的记录
        share_change_df = df[df['has_share_change'] == True]
        
        # 获取唯一的conid集合
        share_change_conids = set(share_change_df['conid'].unique())
        
        logger.info(f"从CSV文件中找到 {len(share_change_conids)} 个有股数变动的conid")
        
        # 生成要删除的conid列表到txt文件
        conids_to_delete = sorted(list(share_change_conids))
        conids_txt_file = os.path.join(os.path.dirname(__file__), "conids_to_delete.txt")
        with open(conids_txt_file, 'w', encoding='utf-8') as f:
            f.write(','.join(map(str, conids_to_delete)))
        logger.info(f"已生成要删除的conid列表到文件: {conids_txt_file}")
        
        # 打印这些conid及其对应的symbol
        for _, row in share_change_df[['conid', 'ib_symbol']].drop_duplicates().iterrows():
            logger.info(f"  conid: {row['conid']}, symbol: {row['ib_symbol']}")
        
        return list(share_change_conids)
    
    def delete_conid_data(self, conids_batch):
        """删除一批conid对应的数据"""
        batch_size = len(conids_batch)
        logger.info(f"开始删除 {batch_size} 个conid的数据: {conids_batch}")
        
        # 转换conid为字符串，因为数据库中symbol存储为字符串
        conid_strs = [str(conid) for conid in conids_batch]
        
        deleted_overview = 0
        deleted_bardata = 0
        
        try:
            with self.target_db.db.atomic():
                # 1. 删除DbBarOverview表中的记录（只删除完全匹配的symbol）
                logger.info("删除DbBarOverview表中的记录...")
                for conid_str in conid_strs:
                    # 只删除symbol完全等于conid的记录，不删除LIKE模式的
                    overview_count = self.target_db.db.execute_sql(
                        "DELETE FROM dbbaroverview WHERE symbol = %s",
                        (conid_str,)
                    ).rowcount
                    
                    deleted_overview += overview_count
                    
                    if overview_count > 0:
                        logger.info(f"  conid {conid_str}: 删除overview记录 {overview_count} 条")
                
                # 2. 删除DbBarData表中的记录（只删除完全匹配的symbol）
                logger.info("删除DbBarData表中的记录...")
                for conid_str in conid_strs:
                    # 只删除symbol完全等于conid的记录，不删除LIKE模式的
                    bardata_count = self.target_db.db.execute_sql(
                        "DELETE FROM dbbardata WHERE symbol = %s",
                        (conid_str,)
                    ).rowcount
                    
                    deleted_bardata += bardata_count
                    
                    if bardata_count > 0:
                        logger.info(f"  conid {conid_str}: 删除bardata记录 {bardata_count} 条")
            
            logger.info(f"批次删除完成: overview {deleted_overview} 条, bardata {deleted_bardata} 条")
            
        except Exception as e:
            logger.error(f"删除数据时出错: {e}")
            raise
        
        return deleted_overview, deleted_bardata
    
    def delete_all_share_change_data(self, batch_size=5):
        """删除所有有股数变动的标的数据"""
        logger.info("开始删除所有有股数变动的标的数据...")
        
        # 1. 获取有股数变动的conid集合
        share_change_conids = self.load_share_change_conids()
        
        if not share_change_conids:
            logger.info("没有找到需要删除的conid")
            return
        
        logger.info(f"总共需要删除 {len(share_change_conids)} 个conid的数据")
        
        # 2. 分批删除
        total_overview_deleted = 0
        total_bardata_deleted = 0
        batch_count = 0
        
        for i in range(0, len(share_change_conids), batch_size):
            batch_conids = share_change_conids[i:i + batch_size]
            batch_count += 1
            
            logger.info(f"\n========== 第 {batch_count} 批 ==========")
            
            overview_deleted, bardata_deleted = self.delete_conid_data(batch_conids)
            total_overview_deleted += overview_deleted
            total_bardata_deleted += bardata_deleted
            
            logger.info(f"第 {batch_count} 批删除完成")
        
        logger.info(f"\n========== 删除完成 ==========")
        logger.info(f"共处理 {batch_count} 批")
        logger.info(f"总删除记录: overview {total_overview_deleted} 条, bardata {total_bardata_deleted} 条")
    
    def preview_delete_data(self):
        """预览将要删除的数据统计"""
        logger.info("预览将要删除的数据...")
        
        share_change_conids = self.load_share_change_conids()
        
        if not share_change_conids:
            logger.info("没有找到需要删除的conid")
            return
        
        conid_strs = [str(conid) for conid in share_change_conids]
        
        total_overview = 0
        total_bardata = 0
        
        for conid_str in conid_strs:
            # 统计overview记录数（只统计完全匹配的symbol）
            overview_result = self.target_db.db.execute_sql(
                "SELECT COUNT(*) FROM dbbaroverview WHERE symbol = %s",
                (conid_str,)
            ).fetchone()
            overview_count = overview_result[0] if overview_result else 0
            
            # 统计bardata记录数（只统计完全匹配的symbol）
            bardata_result = self.target_db.db.execute_sql(
                "SELECT COUNT(*) FROM dbbardata WHERE symbol = %s",
                (conid_str,)
            ).fetchone()
            bardata_count = bardata_result[0] if bardata_result else 0
            
            total_overview += overview_count
            total_bardata += bardata_count
            
            if overview_count > 0 or bardata_count > 0:
                logger.info(f"  conid {conid_str}: overview {overview_count} 条, bardata {bardata_count} 条")
        
        logger.info(f"\n预览统计:")
        logger.info(f"总计将删除: overview {total_overview} 条, bardata {total_bardata} 条")


def main():
    """主函数"""
    # CSV文件路径
    csv_file = "share_changes_20250901_214456.csv"
    csv_path = os.path.join(os.path.dirname(__file__), csv_file)
    
    if not os.path.exists(csv_path):
        logger.error(f"CSV文件不存在: {csv_path}")
        return
    
    # 创建删除器
    deleter = ShareChangeDataDeleter(csv_path)
    
    # 先预览要删除的数据
    deleter.preview_delete_data()
    
    # 确认是否继续
    confirm = input("\n确认删除以上数据？输入 'YES' 继续，其他任意键取消: ")
    if confirm != 'YES':
        logger.info("操作已取消")
        return
    
    # 执行删除
    deleter.delete_all_share_change_data(batch_size=5)


if __name__ == "__main__":
    main()