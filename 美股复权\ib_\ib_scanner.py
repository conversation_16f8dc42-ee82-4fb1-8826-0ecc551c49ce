#!/usr/bin/env python3
"""
IB Scanner - 独立的Interactive Brokers股票扫描器
从IB获取美股活跃股票列表并保存为CSV文件
"""

import os
import pandas as pd
import typer
from typing import Dict, Any, List
from itertools import product
from ib_async import IB, ScannerSubscription, TagValue
from vnpy.trader.utility import load_json
import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志

# 全局变量
DEFAULT_CSV_FILENAME = 'scanner_unique_stk_us_1500.csv'

class IbScanner:
    """IB股票扫描器类"""
    
    def __init__(self, connect_setting: Dict[str, Any]):
        self.connect_setting = connect_setting
        self.ib = IB()
        self.data = []
        self.pair = []

    def connect(self):
        """连接到IB"""
        self.ib.connect(
            self.connect_setting["TWS地址"],
            self.connect_setting["TWS端口"],
            clientId=self.connect_setting["客户号"] + 1
        )

    def disconnect(self):
        """断开IB连接"""
        self.ib.disconnect()

    def single_scanner(self, instrument: str, locationCode: str, avgUsdVolumeAbove: str, 
                      avgUsdVolumeBelow: str = None, avg: bool = True) -> int:
        """执行单次扫描"""
        sub = ScannerSubscription(
            instrument=instrument, 
            locationCode=locationCode, 
            scanCode='MOST_ACTIVE_AVG_USD'
        )
        
        try:
            above_tag = "avgUsdVolumeAbove" if avg else "usdVolumeAbove"
            tagValues = [TagValue(above_tag, avgUsdVolumeAbove)]
            
            if avgUsdVolumeBelow:
                below_tag = "avgUsdVolumeBelow" if avg else "usdVolumeBelow"
                tagValues.append(TagValue(below_tag, avgUsdVolumeBelow))

            scanData = self.ib.reqScannerData(sub, [], tagValues)
            num_scanData = len(scanData)
            
            if num_scanData > 0:
                self.pair.append((instrument, locationCode))

            for sd in scanData:
                temp = {
                    'instrument': instrument,
                    'locationCode': locationCode,
                    "rank": sd.rank,
                }
                temp.update(sd.contractDetails.contract.__dict__)
                temp.update(sd.contractDetails.__dict__)
                self.data.append(temp)

            return num_scanData

        except Exception as e:
            logger.error(f"扫描出错: {e}")
            return 0

    def iter_scanner(self, instrument: str, locationCode: str, avgUsdVolumeAbove: str, 
                    avgUsdVolumeBelow: str = None, avg: bool = True):
        """迭代扫描，处理分页"""
        min_above = avgUsdVolumeAbove
        num_scanData = self.single_scanner(instrument, locationCode, avgUsdVolumeAbove, avgUsdVolumeBelow, avg=avg)
        
        while num_scanData == 50:
            avgUsdVolumeAbove = str(int(avgUsdVolumeAbove) * 2) # if avgUsdVolumeAbove != '0' else '100000000'
            num_scanData = self.single_scanner(instrument, locationCode, avgUsdVolumeAbove, avgUsdVolumeBelow, avg=avg)

        if avgUsdVolumeAbove == min_above:
            return

        max_below = avgUsdVolumeAbove
        iter_range = int(max_below) - int(min_above)
        last_range = iter_range // 2

        last_avgUsdVolumeBelow = max_below
        last_avgUsdVolumeAbove = str(int(max_below) - last_range)

        num_scanData = self.single_scanner(instrument, locationCode, last_avgUsdVolumeAbove, last_avgUsdVolumeBelow, avg=avg)
        logger.info(f'{last_avgUsdVolumeBelow} > {instrument} {locationCode} > {last_avgUsdVolumeAbove} : {num_scanData}')

        while int(last_avgUsdVolumeAbove) >= int(min_above):
            if num_scanData == 50:
                last_range //= 2
                last_avgUsdVolumeAbove = str(int(last_avgUsdVolumeAbove) + last_range)
            elif (num_scanData == 0 and last_avgUsdVolumeAbove != min_above) or 0 < num_scanData <= 5:
                last_range *= 2
                last_avgUsdVolumeBelow = last_avgUsdVolumeAbove
                last_avgUsdVolumeAbove = str(max(int(min_above), int(last_avgUsdVolumeAbove) - last_range))
            elif 5 < num_scanData < 50:
                last_avgUsdVolumeBelow = last_avgUsdVolumeAbove
                last_avgUsdVolumeAbove = str(max(int(min_above), int(last_avgUsdVolumeAbove) - last_range))
            else:
                break

            if last_avgUsdVolumeAbove == last_avgUsdVolumeBelow:
                break
                
            num_scanData = self.single_scanner(instrument, locationCode, last_avgUsdVolumeAbove, last_avgUsdVolumeBelow, avg=avg)
            logger.info(f'{last_avgUsdVolumeBelow} > {instrument} {locationCode} > {last_avgUsdVolumeAbove} : {num_scanData}')

    def run_scanner(self, output_file: str = DEFAULT_CSV_FILENAME, volume_above: str = "10") -> pd.DataFrame:
        """运行完整扫描流程"""
        stk_instruments = ['STK']
        stk_lc_us = ['STK.US']
        stk_product = list(product(stk_instruments, stk_lc_us))

        for instrument, locationCode in stk_product:
            self.iter_scanner(instrument, locationCode, volume_above)

        # 数据清理和去重
        df = pd.DataFrame(self.data).drop_duplicates(subset=['conId'], keep='first')
        df = df.dropna(axis=1, how='all')
        df = df.loc[:, (df != 0).any(axis=0)]
        df = df.loc[:, (df != 1).any(axis=0)]
        df = df.drop(columns=['contract', 'rank', 'comboLegs', 'secIdList'], errors='ignore')
        df = df.loc[:, (df != '').any(axis=0)]
        
        # 保存到文件
        df.to_csv(output_file, index=False)
        logger.success(f"扫描完成，数据已保存到 {output_file}，共 {len(df)} 条记录")
        
        return df


def main(
    output_file: str = typer.Option(DEFAULT_CSV_FILENAME, "--output", "-o", help="输出CSV文件路径"),
    volume_above: str = typer.Option("15000000", "--volume-above", "-v", help="最小平均美元成交量阈值"),
    quiet: bool = typer.Option(False, "--quiet", "-q", help="静默模式，不输出日志"),
):
    """
    IB股票扫描器 - 获取美股活跃股票列表
    
    从Interactive Brokers获取美股市场的活跃股票信息并保存为CSV文件。
    """
    # 根据quiet参数调整日志级别
    if quiet:
        logger.remove()
        logger.add(f"logs/{log_file_name}", level="ERROR", format="{time} | {level: <8} | {name}:{function}:{line} - {message}", rotation="00:00", filter=__name__)
    
    try:
        # 加载IB连接配置
        connect_setting: Dict[str, Any] = load_json("connect_ib.json")
        
        # 创建扫描器实例
        scanner = IbScanner(connect_setting)
        
        # 连接IB
        logger.info("正在连接到IB...")
        scanner.connect()
        logger.success("IB连接成功")
        
        # 运行扫描
        logger.info(f"开始扫描美股活跃股票，成交量阈值: {volume_above}...")
        df = scanner.run_scanner(output_file, volume_above)
        
        # 断开连接
        scanner.disconnect()
        logger.info("IB连接已断开")
        
        # 输出结果统计
        logger.success(f"✅ 扫描完成！共获取 {len(df)} 只股票，数据已保存到: {output_file}")
        
    except Exception as e:
        logger.error(f"❌ 扫描过程中发生错误: {str(e)}")
        raise typer.Exit(1)


if __name__ == "__main__":
    typer.run(main)