Positions By Model  模特职位Copy Location  复制位置
The function IBApi.EClient.reqPositionsMulti can be used with any account structure to subscribe to positions updates for multiple accounts and/or models. The account and model parameters are optional if there are not multiple accounts or models available. It is more efficient to use this function for a specific subset of accounts than using IBApi.EClient.reqPositions. A profile name can be accepted in place of group in the account parameter.
IBApi.EClient.reqPositionsMulti 函数可用于任何账户结构，以订阅多个账户和/或模型的头寸更新。如果没有多个账户或模型可用，则 account 和 model 参数为可选参数。对于特定的账户子集，使用此函数比使用 IBApi.EClient.reqPositions 更高效。account 参数中可以使用配置文件名称代替 group。

Request Positions By Model
按模型请求职位Copy Location  复制位置
EClient.reqPositionsMulti(
EClient.reqPositionsMulti（
requestId: int. Request’s identifier.
requestId： int，请求的标识符。

account: String. If an account Id is provided, only the account’s positions belonging to the specified model will be delivered.
account： 字符串。如果提供了账户 ID，则仅传递该账户属于指定模型的仓位。

modelCode: String. The code of the model’s positions we are interested in.
modelCode： 字符串。我们感兴趣的模型位置的代码。
)

Requests position subscription for account and/or model Initially all positions are returned, and then updates are returned for any position changes in real time.
请求帐户和/或模型的仓位订阅最初返回所有仓位，然后实时返回任何仓位变化的更新。

Python
Java
C++
C#
VB.NET
self.reqPositionsMulti(requestid, "U1234567", "")
 

Code example:  代码示例：

from ibapi.client import *
from ibapi.wrapper import *
import threading
import time
class TradingApp(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self,self)
            
    def positionMulti(self, reqId: int, account: str, modelCode: str, contract: Contract, pos: Decimal, avgCost: float):
       print("PositionMulti. RequestId:", reqId, "Account:", account, "ModelCode:", modelCode, "Contract:", contract, ",Position:", pos, "AvgCost:", avgCost)         
        
    def positionMultiEnd(self, reqId: int):
        print("")
        print("PositionMultiEnd. RequestId:", reqId)       
def websocket_con():
    app.run()
    
app = TradingApp()      
app.connect("127.0.0.1", 7497, clientId=1)
con_thread = threading.Thread(target=websocket_con, daemon=True)
con_thread.start()
time.sleep(1) 
app.reqPositionsMulti(2, "DU1234567", "")  #To specify a U-account number
time.sleep(1)
app.reqPositionsMulti(3, "Group1", "")     #To specify a Financial Advisor Group / Profile 
time.sleep(1)
 

Receive Positions By Model
按型号接收职位Copy Location  复制位置
EWrapper.positionMulti(
requestId: int. The id of request
requestId: int. 请求的 id

account: String. The account holding the position.
account： 字符串。持仓账户。

modelCode: String. The model code holding the position.
modelCode： 字符串。持有仓位的模型代码。

contract: Contract. The position’s Contract
contract： 合同。该职位的合同

pos: decimal. The number of positions held.
pos： 小数。持有的仓位数量。

avgCost: double. The average cost of the position.
avgCost： double。持仓的平均成本。
)

Provides the portfolio’s open positions.
提供投资组合的未平仓头寸。

Python
Java
C++
C#
VB.NET
def positionMulti(self, reqId: int, account: str, modelCode: str, contract: Contract, pos: Decimal, avgCost: float):
  print("PositionMulti. RequestId:", reqId, "Account:", account, "ModelCode:", modelCode, "Contract:", contract, ",Position:", pos, "AvgCost:", avgCost)
 

EWrapper.positionMultiEnd(
requestId: int. The id of request
requestId: int. 请求的 id
)

Indicates all the positions have been transmitted.
表示所有位置均已传输。

Python
Java
C++
C#
VB.NET
def positionMultiEnd(self, reqId: int):
  print("PositionMultiEnd. RequestId:", reqId)
 

Cancel Positions By Model
按模型取消职位Copy Location  复制位置
EClient.cancelPositionsMulti (
EClient.cancelPositionsMulti（
requestId: int. The identifier of the request to be canceled.
requestId： int，待取消请求的标识符。

)

Cancels positions request for account and/or model.
取消帐户和/或模型的职位请求。

Python
Java
C++
C#
VB.NET
self.cancelPositionsMulti(requestid)