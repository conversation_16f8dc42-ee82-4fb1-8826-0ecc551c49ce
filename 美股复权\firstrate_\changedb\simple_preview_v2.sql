-- 简化的预览脚本（使用永久表避免重复引用问题）

-- 1. 查看有多少个基础symbol需要处理
SELECT COUNT(DISTINCT SUBSTRING_INDEX(symbol, '_', 1)) as base_symbol_count
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE symbol LIKE '%\_%' ESCAPE '\\';

-- 2. 查看前20个基础symbol（避免输出太长）
SELECT DISTINCT SUBSTRING_INDEX(symbol, '_', 1) as base_symbol
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE symbol LIKE '%\_%' ESCAPE '\\'
ORDER BY base_symbol
LIMIT 20;

-- 3. 统计每个基础symbol的变体数量（前20个）
SELECT
    SUBSTRING_INDEX(symbol, '_', 1) as base_symbol,
    COUNT(*) as variant_count
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE symbol LIKE '%\_%' ESCAPE '\\'
GROUP BY SUBSTRING_INDEX(symbol, '_', 1)
ORDER BY base_symbol
LIMIT 20;

-- 4. 预估会影响多少条记录（使用永久表）
-- 创建永久表进行测试
DROP TABLE IF EXISTS temp_test_symbols;
CREATE TABLE temp_test_symbols AS
SELECT DISTINCT SUBSTRING_INDEX(symbol, '_', 1) as base_symbol
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE symbol LIKE '%\_%' ESCAPE '\\';

-- 统计会被删除的记录数（现在可以在UNION中重复使用）
SELECT
    'dbbardata' as table_name,
    COUNT(*) as affected_records
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbardata
WHERE EXISTS (
    SELECT 1 FROM temp_test_symbols t
    WHERE vnpy_stk_us_ib_m_2206_250814bak.dbbardata.symbol = t.base_symbol
       OR vnpy_stk_us_ib_m_2206_250814bak.dbbardata.symbol LIKE CONCAT(t.base_symbol, '\_%') ESCAPE '\\'
)
UNION ALL
SELECT
    'dbbaroverview' as table_name,
    COUNT(*) as affected_records
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE EXISTS (
    SELECT 1 FROM temp_test_symbols t
    WHERE vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview.symbol = t.base_symbol
       OR vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview.symbol LIKE CONCAT(t.base_symbol, '\_%') ESCAPE '\\'
);

-- 清理测试表
DROP TABLE temp_test_symbols;