import pandas as pd
from pathlib import Path

# 读取Excel文件
file_path = 'his/美股品种信息20250801.xlsx'
df = pd.read_excel(file_path, sheet_name='稳定ID', engine='openpyxl')

# 根据图片中的local_symbol筛选数据
# 图片中显示的symbol: ORLY, LTM, GMAB, CBIO, SCNI, LGHL, SAFX, PBM, HAFN, BWLP
target_symbols = ['ORLY', 'LTM', 'GMAB', 'CBIO', 'SCNI', 'LGHL', 'SAFX', 'PBM', 'HAFN', 'BWLP']

# 筛选数据
filtered_df = df[df['local_symbol'].isin(target_symbols)]
print(f'根据symbol筛选后数据: {len(filtered_df)} 行')

if len(filtered_df) > 0:
    print('筛选出的数据:')
    # 显示关键列
    key_columns = ['ID', 'local_symbol', '市值（亿美元）', '总股本（亿）', '收盘价_1M']
    available_columns = [col for col in key_columns if col in filtered_df.columns]
    print(filtered_df[available_columns].to_string())

    # 保存为新的Excel文件
    output_file = 'his/筛选数据_20250801.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        filtered_df.to_excel(writer, sheet_name='稳定ID', index=False)

    print(f'数据已保存到: {output_file}')
else:
    print('未找到匹配的数据')

    # 检查一下有哪些symbol
    print('\\n文件中的前20个symbol:')
    print(df['local_symbol'].head(20).tolist())
