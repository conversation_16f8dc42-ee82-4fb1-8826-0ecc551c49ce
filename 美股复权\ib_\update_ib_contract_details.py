import traceback
from typing import List, Union, Set, Optional, Dict, Any, Tuple
from datetime import datetime
import threading
import queue
import time
import sys
import os
import xml.etree.ElementTree as ET
from multiprocessing import Process, Queue, Event, Lock
from vnpy.trader.utility import load_json, save_json

import typer
from typing_extensions import Annotated

from ib_async import Stock, ContractDetails, RequestError, IB
from loguru import logger
from time import sleep
import asyncio


# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, IbContractDetail, ContractTime, IbFundamentals, IbProduct # 导入IbProduct
from firstrate_.update_conid import get_conid_mapping

def contract_details_to_dict(conid: int, detail: ContractDetails) -> Dict[str, Any]:
    """将ContractDetails对象转换为可保存到数据库的字典格式
    
    Args:
        conid: 合约ID
        detail: IB的ContractDetails对象
        
    Returns:
        包含所有必要字段的字典
    """    
    return {
        'conid': conid,
        
        # Contract字段
        'sec_type': detail.contract.secType or None,
        'symbol': detail.contract.symbol or None,
        'last_trade_date_or_contract_month': detail.contract.lastTradeDateOrContractMonth or None,
        'strike': detail.contract.strike,
        'right': detail.contract.right or None,
        'multiplier': detail.contract.multiplier,
        'exchange': detail.contract.exchange or None,
        'primary_exchange': detail.contract.primaryExchange or None,
        'currency': detail.contract.currency or None,
        'local_symbol': detail.contract.localSymbol or None,
        'trading_class': detail.contract.tradingClass or None,
        'include_expired': detail.contract.includeExpired,
        'sec_id_type': detail.contract.secIdType or None,
        'sec_id': detail.contract.secId or None,
        'description': detail.contract.description or None,
        'issuer_id': detail.contract.issuerId or None,
        
        # ContractDetails字段
        'market_name': detail.marketName or None,
        'min_tick': detail.minTick,
        'order_types': detail.orderTypes or None,
        'valid_exchanges': detail.validExchanges or None,
        'price_magnifier': detail.priceMagnifier,
        'under_conid': detail.underConId,
        'long_name': detail.longName or None,
        'contract_month': detail.contractMonth or None,
        'industry': detail.industry or None,
        'category': detail.category or None,
        'subcategory': detail.subcategory or None,
        'time_zone_id': detail.timeZoneId or None,
        'trading_hours': detail.tradingHours or None,
        'liquid_hours': detail.liquidHours or None,
        'ev_rule': detail.evRule or None,
        'ev_multiplier': detail.evMultiplier,
        'md_size_multiplier': detail.mdSizeMultiplier,
        'agg_group': detail.aggGroup,
        'under_symbol': detail.underSymbol or None,
        'under_sec_type': detail.underSecType or None,
        'market_rule_ids': detail.marketRuleIds or None,
        'sec_id_list': str(detail.secIdList) if detail.secIdList else None,
        'real_expiration_date': detail.realExpirationDate or None,
        'last_trade_time': detail.lastTradeTime or None,
        'stock_type': detail.stockType or None,
        'min_size': detail.minSize,
        'size_increment': detail.sizeIncrement,
        'suggested_size_increment': detail.suggestedSizeIncrement,
        'cusip': detail.cusip or None,
        'ratings': detail.ratings or None,
        'desc_append': detail.descAppend or None,
        'bond_type': detail.bondType or None,
        'coupon_type': detail.couponType or None,
        'callable': detail.callable,
        'putable': detail.putable,
        'coupon': detail.coupon,
        'convertible': detail.convertible,
        'maturity': detail.maturity or None,
        'issue_date': detail.issueDate or None,
        'next_option_date': detail.nextOptionDate or None,
        'next_option_type': detail.nextOptionType or None,
        'next_option_partial': detail.nextOptionPartial,
        'notes': detail.notes or None,
    }

app = typer.Typer()

def _get_latest_product_conids() -> Set[int]:
    """从IbProduct表获取标记为is_latest=True的conid列表"""
    logger.info("正在从数据库获取最新的产品conid...")
    conids = set()
    try:        
        query = IbProduct.select(IbProduct.conid).where(IbProduct.is_latest == True).tuples()
        for conid_tuple in query:
            if conid_tuple[0]:
                conids.add(conid_tuple[0])
        logger.info(f"已获取{len(conids)}个最新的产品conid。")
    except Exception as e:
        logger.error(f"获取最新产品conid时出错: {str(e)}\n{traceback.format_exc()}")
    return conids

class IbContractDetailUpdater:
    """IB合约详情更新器"""
    
    # 定义类级别的进程锁
    _invalid_conids_lock = Lock()
    _invalid_snapshot_conids_lock = Lock()
    _no_head_time_conids_lock = Lock()
    _base_client_id = None  # 基础clientId
    
    def __init__(self):
        """初始化"""
        self.ib = None
        self.failed_conids = set()
        self.conid_contract_map = {}
        self.report_snapshot_dir = os.path.join(os.path.dirname(__file__), "ReportSnapshot")
        os.makedirs(self.report_snapshot_dir, exist_ok=True)
        self.latest_product_conids = _get_latest_product_conids()
        
        # 添加无效conid相关的属性
        self.invalid_conids_file = "invalid_conids.json"  # 无效合约
        self.invalid_snapshot_conids_file = "invalid_snapshot_conids.json"  # 无效基本面数据
        self.no_head_time_conids_file = "no_head_time_conids.json"  # 无历史数据
        self.invalid_conids = set()
        self.invalid_snapshot_conids = set()
        self.no_head_time_conids = set()
        self._load_invalid_conids()

    def connect(self, ip: str = None, port: int = None, client_id: int = None):
        """连接到IB"""        
        # 从connect_ib.json读取连接信息作为默认值
        connect_filename = 'connect_ib.json'
        setting = load_json(connect_filename)
        default_ip = setting.get("TWS地址", '**************')
        default_port = setting.get("TWS端口", 4012)
        
        # 使用传入的参数（非None）替换默认值
        final_ip = ip if ip is not None else default_ip
        final_port = port if port is not None else default_port
        
        # 如果没有指定client_id，则使用基础client_id
        if client_id is None:
            if self._base_client_id is None:
                self._base_client_id = setting.get("客户号", 611)
            client_id = self._base_client_id
        
        logger.info(f"连接信息：IP={final_ip}, 端口={final_port}, 客户号={client_id}")
                
        if self.ib is None or not self.ib.isConnected():
            self.ib = IB()
            self.ib.RaiseRequestErrors = True
            self.ib.connect(final_ip, final_port, clientId=client_id)
            
    def disconnect(self):
        """断开IB连接,同时保存无效conid列表"""
        if self.ib:
            try:
                # 保存无效conid列表
                self._save_invalid_conids()
            except Exception as e:
                logger.error(f"断开连接时保存无效conid列表出错: {str(e)}\n{traceback.format_exc()}")
            finally:
                self.ib.disconnect()

    def __del__(self):
        """析构函数,确保在对象销毁时保存无效conid列表"""
        try:
            self._save_invalid_conids()
        except Exception as e:
            logger.error(f"对象销毁时保存无效conid列表出错: {str(e)}")
        finally:
            self.disconnect()

    def fetch_contract_details(self, conids_to_fetch: Set[int], data_queue: queue.Queue, fetching_completed_event: threading.Event):
        """获取合约详情并放入队列
        
        Args:
            conids_to_fetch: 合约conid的集合
            data_queue: 数据队列
            fetching_completed_event: 获取完成事件
        """
        try:
            logger.info(f"开始获取{len(conids_to_fetch)}个合约的详情...")
            
            for conid in conids_to_fetch:
                try:
                    # 创建Contract对象
                    contract = Stock(conId=conid)
                    
                    # 请求合约详情
                    details = self.ib.reqContractDetails(contract)
                    
                    if not details:
                        logger.warning(f"未找到conid={conid}的合约详情")
                        self.failed_conids.add(conid)
                        continue
                        
                    # 只取第一个详情（通常只有一个）
                    detail = details[0]
                    self.conid_contract_map[conid] = detail.contract
                    
                    # 将ContractDetails对象转换为字典并放入队列
                    data = contract_details_to_dict(conid, detail)
                    data_queue.put(data)
                    logger.info(f"已获取conid={conid}的合约详情")
                    
                except RequestError as e:
                    if e.code == 200:  # No security definition
                        error_msg = e.message.lower()
                        if "no security definition" in error_msg:
                            logger.warning(f"conid={conid}是无效的合约，将添加到无效合约列表")
                            self._add_invalid_conid(conid)
                        elif "ambiguous" in error_msg:
                            logger.warning(f"conid={conid}的合约定义不明确，需要更多信息")
                        else:
                            logger.warning(f"conid={conid}请求合约详情时出现错误: {error_msg}")
                    else:
                        logger.error(f"获取conid={conid}的合约详情时出错: [{e.code}] {e.message}")
                    self.failed_conids.add(conid)
                    continue
                    
                except Exception as e:
                    logger.error(f"获取conid={conid}的合约详情时出错: {str(e)}\n{traceback.format_exc()}")
                    self.failed_conids.add(conid)
                    continue
                    
        except Exception as e:
            logger.error(f"获取合约详情时发生错误: {str(e)}\n{traceback.format_exc()}")
            raise
        finally:
            fetching_completed_event.set() # 标记获取完成

    def _get_qualified_contract(self, conid: int) -> Optional[Stock]:
        """获取或请求合格的合约对象，并缓存"""            
        if conid in self.conid_contract_map:
            return self.conid_contract_map[conid]

        try:
            contract = Stock(conId=conid)
            details = self.ib.reqContractDetails(contract)
            
            if details:
                qualified_contract = details[0].contract
                self.conid_contract_map[conid] = qualified_contract
                return qualified_contract
            else:
                logger.warning(f"未找到conid={conid}的合约详情")
                return None
                
        except (ConnectionError, asyncio.exceptions.CancelledError) as e:
            # 连接相关错误，需要重连
            logger.error(f"获取或合格conid={conid}的合约时出现连接错误: {str(e)}")
            raise  # 抛出异常让外层处理重连
            
        except RequestError as e:
            if e.code == 200:  # No security definition
                error_msg = e.message.lower()
                if "no security definition" in error_msg:
                    logger.warning(f"conid={conid}是无效的合约，将添加到无效合约列表")
                    self._add_invalid_conid(conid)
                elif "ambiguous" in error_msg:
                    logger.warning(f"conid={conid}的合约定义不明确，需要更多信息")
                else:
                    logger.warning(f"conid={conid}请求合约详情时出现错误: {error_msg}")
            else:
                logger.error(f"获取conid={conid}的合约详情时出错: [{e.code}] {e.message}")
            return None
            
        except Exception as e:
            logger.error(f"获取或合格conid={conid}的合约时出错: {str(e)}\n{traceback.format_exc()}")
            return None

    def _parse_report_snapshot_xml(self, xml_content: str, conid: int) -> Optional[dict]:
        """解析ReportSnapshot XML并提取所需字段"""
        try:
            root = ET.fromstring(xml_content)
            
            data = {"conid": conid}

            # CoIDs
            coids_elem = root.find(".//CoIDs")
            if coids_elem:
                data["rep_no"] = coids_elem.findtext("CoID[@Type='RepNo']")
                data["company_name"] = coids_elem.findtext("CoID[@Type='CompanyName']")
                data["irs_no"] = coids_elem.findtext("CoID[@Type='IRSNo']")
                data["cik_no"] = coids_elem.findtext("CoID[@Type='CIKNo']")
                data["organization_perm_id"] = coids_elem.findtext("CoID[@Type='OrganizationPermID']")

            # 解析指数成分股信息
            peer_info_elem = root.find(".//peerInfo")
            if peer_info_elem:
                index_constituents = peer_info_elem.findall("Indexconstituet")
                if index_constituents:
                    # 将所有指数名称用分号连接
                    data["index_constituents"] = ";".join(index.text for index in index_constituents)

            # 解析GlobalListingType中的SharesPerListing和MostRecentSplit信息
            # 查找Issues下的Issue元素中的GlobalListingType
            issues_elem = root.find(".//Issues")
            if issues_elem:
                for issue_elem in issues_elem.findall("Issue"):
                    global_listing_type_elem = issue_elem.find("GlobalListingType")
                    if global_listing_type_elem is not None:
                        shares_per_listing_attr = global_listing_type_elem.get("SharesPerListing")
                        if shares_per_listing_attr:
                            try:
                                data["shares_per_listing"] = float(shares_per_listing_attr)
                            except (ValueError, TypeError):
                                data["shares_per_listing"] = None
                        else:
                            data["shares_per_listing"] = None
                    
                    # 解析MostRecentSplit信息
                    most_recent_split_elem = issue_elem.find("MostRecentSplit")
                    if most_recent_split_elem is not None:
                        # 解析拆股日期
                        split_date_str = most_recent_split_elem.get("Date")
                        if split_date_str:
                            try:
                                split_date = datetime.strptime(split_date_str, "%Y-%m-%d")
                                data["most_recent_split_date"] = split_date
                            except (ValueError, TypeError):
                                data["most_recent_split_date"] = None
                        else:
                            data["most_recent_split_date"] = None
                        
                        # 解析拆股比例
                        split_ratio_str = most_recent_split_elem.text
                        if split_ratio_str:
                            try:
                                data["most_recent_split_ratio"] = float(split_ratio_str)
                            except (ValueError, TypeError):
                                data["most_recent_split_ratio"] = None
                        else:
                            data["most_recent_split_ratio"] = None
                    else:
                        data["most_recent_split_date"] = None
                        data["most_recent_split_ratio"] = None
                    
                    break  # 只取第一个Issue的数据

            # CoGeneralInfo
            cogeneralinfo_elem = root.find(".//CoGeneralInfo")
            if cogeneralinfo_elem:
                shares_out_elem = cogeneralinfo_elem.find("SharesOut")
                if shares_out_elem is not None:
                    data["shares_out"] = float(shares_out_elem.text) if shares_out_elem.text else None
                    data["total_float"] = float(shares_out_elem.get("TotalFloat")) if shares_out_elem.get("TotalFloat") else None

            # Ratios
            ratios_elem = root.find(".//Ratios")
            if ratios_elem:
                for group_elem in ratios_elem.findall("Group"):
                    for ratio_elem in group_elem.findall("Ratio"):
                        field_name = ratio_elem.get("FieldName")
                        if field_name:
                            value = ratio_elem.text
                            # 尝试转换为数值类型，如果失败则保留字符串
                            try:
                                if ratio_elem.get("Type") == "N": # Numeric
                                    # 检查是否为特殊的无数据值
                                    if value and float(value) == -99999.99000:
                                        data[field_name.lower()] = None
                                    else:
                                        data[field_name.lower()] = float(value) if value else None
                                elif ratio_elem.get("Type") == "D": # Date
                                    # 处理日期格式，只取日期部分
                                    data[field_name.lower()] = datetime.strptime(value.split("T")[0], "%Y-%m-%d") if value else None
                                else:
                                    data[field_name.lower()] = value
                            except (ValueError, TypeError):
                                data[field_name.lower()] = value # Fallback to string if conversion fails

            return data
            
        except ET.ParseError as e:
            logger.error(f"解析conid={conid}的ReportSnapshot XML时出错: {e}")
            return None
        except Exception as e:
            logger.error(f"处理conid={conid}的ReportSnapshot数据时发生未知错误: {str(e)}\n{traceback.format_exc()}")
            return None

    def _update_report_snapshots(self, conids_to_process: Set[int], data_queue: queue.Queue, fetching_completed_event: threading.Event):
        """下载并保存ReportSnapshot XML数据，并解析保存到数据库"""
        logger.info(f"开始检查并更新ReportSnapshot数据，共{len(conids_to_process)}个conid...")
        
        # 排除已知的无效基本面数据conid
        conids_to_process = conids_to_process - self.invalid_snapshot_conids
        
        current_month_str = datetime.now().strftime("%Y%m")
        now = datetime.now()

        # 1. 从IbFundamentals表中找出所有update_time是当前月份的conid
        conids_already_up_to_date_in_db = set()
        try:
            query = (
                IbFundamentals.select(IbFundamentals.conid)
                .where(
                    (IbFundamentals.update_time.year == now.year) &
                    (IbFundamentals.update_time.month == now.month)
                )
                .tuples()
            )
            for conid_tuple in query:
                conids_already_up_to_date_in_db.add(conid_tuple[0])
        except Exception as e:
            logger.warning(f"查询现有基本面数据时出错: {e}，将尝试全部更新。")
            conids_already_up_to_date_in_db.clear() # 如果查询出错，则认为所有都需要更新

        # 2. 需要更新的conid是conids_to_process中除去IbFundamentals中已是最新conid的差集
        conids_needing_update = conids_to_process - conids_already_up_to_date_in_db

        if not conids_needing_update:
            logger.info("所有ReportSnapshot数据均已是最新，无需更新。")
            fetching_completed_event.set()
            return

        logger.info(f"需要获取ReportSnapshot的conid数量 (数据库层面判断): {len(conids_needing_update)}")
        fetched_count = 0
        for conid in conids_needing_update:
            report_xml = None
            source_description = "network"
            file_path = os.path.join(self.report_snapshot_dir, f"{conid}.xml")

            # 尝试从本地文件读取
            if os.path.exists(file_path):
                try:
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path)).strftime("%Y%m") # 使用修改时间更准确
                    if file_mtime == current_month_str:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            report_xml = f.read()
                        source_description = "local file"
                        logger.info(f"conid={conid}: 从本地文件读取当月ReportSnapshot。")
                    else:
                        logger.info(f"conid={conid}: 本地ReportSnapshot文件过旧 ({file_mtime})，将从网络获取。")
                except Exception as e:
                    logger.warning(f"读取conid={conid}本地ReportSnapshot文件失败: {e}，将从网络获取。")
            else:
                logger.info(f"conid={conid}: 本地ReportSnapshot文件不存在，将从网络获取。")

            # 如果未从本地文件获取到，则发起网络请求
            if report_xml is None:
                try:
                    contract = self._get_qualified_contract(conid)
                    if not contract:
                        continue
                        
                    report_xml = self.ib.reqFundamentalData(contract, "ReportSnapshot")

                    if report_xml:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(report_xml)
                        logger.info(f"conid={conid}: 已从网络获取并保存ReportSnapshot到 {file_path}")
                    else:
                        logger.warning(f"conid={conid}未获取到ReportSnapshot数据 (网络请求)。")
                        self.failed_conids.add(conid)
                        continue # 未获取到数据，跳过解析

                except RequestError as e:
                    if e.code == 430:  # Fundamentals data not available
                        logger.warning(f"conid={conid}没有基本面数据，将添加到无效基本面数据列表")
                        self._add_invalid_conid(conid, is_snapshot=True)
                    self.failed_conids.add(conid)
                    continue
                    
                except Exception as e:
                    logger.error(f"获取conid={conid}的ReportSnapshot时出错 (网络请求): {str(e)}\n{traceback.format_exc()}")
                    self.failed_conids.add(conid)
                    continue
            
            # 解析数据并添加到队列
            if report_xml:
                parsed_data = self._parse_report_snapshot_xml(report_xml, conid)
                if parsed_data:
                    data_queue.put(parsed_data)
                    fetched_count += 1

        logger.info(f"ReportSnapshot数据获取和保存阶段完成，成功处理{fetched_count}条记录。")
        fetching_completed_event.set()

    def update_contract_details(self, conids: Optional[Union[int, List[int], Set[int]]] = None) -> None:
        """更新合约详情
        
        Args:
            conids: 单个conid或conid列表/集合。如果为None，则使用latest_product_conids
        """
        try:
            # 确保连接
            self.connect()
            
            # 确定要处理的conids
            if conids is None:
                conids_to_process = self.latest_product_conids
            elif isinstance(conids, (int, str)):
                conids_to_process = {int(conids)}
            else:
                conids_to_process = set(int(conid) for conid in conids)

            if not conids_to_process:
                logger.info("没有指定conid或没有最新的产品conid，跳过合约详情和基本面数据更新。")
                return

            # 重置状态和映射
            self.failed_conids.clear()
            self.conid_contract_map.clear() # 清空contract map

            # 为合约详情数据创建队列和事件
            contract_detail_queue = queue.Queue()
            contract_detail_fetching_completed = threading.Event()
            # 为基本面数据创建队列和事件
            fundamentals_queue = queue.Queue()
            fundamentals_fetching_completed = threading.Event()

            # 创建并启动保存线程
            contract_detail_save_thread = threading.Thread(
                target=db_manager.process_and_save_queue,
                args=(contract_detail_queue, contract_detail_fetching_completed, IbContractDetail)
            )
            fundamentals_save_thread = threading.Thread(
                target=db_manager.process_and_save_queue,
                args=(fundamentals_queue, fundamentals_fetching_completed, IbFundamentals)
            )

            contract_detail_save_thread.start()
            fundamentals_save_thread.start()
            
            # 在主线程中获取数据
            # 获取合约详情
            self.fetch_contract_details(conids_to_process, contract_detail_queue, contract_detail_fetching_completed)
            
            # 等待合约详情保存线程完成
            contract_detail_save_thread.join()

            # 更新ReportSnapshot的函数
            self._update_report_snapshots(conids_to_process, fundamentals_queue, fundamentals_fetching_completed)

            # 等待基本面数据保存线程完成
            fundamentals_save_thread.join()

            # 汇总未获取到的conid
            if self.failed_conids:
                logger.warning(f"以下{len(self.failed_conids)}个conid未能成功获取所有数据：{sorted(self.failed_conids)}")
                    
        except Exception as e:
            logger.error(f"更新合约详情时发生错误: {str(e)}\n{traceback.format_exc()}")
            raise
            
        finally:
            self.disconnect()

    def _load_invalid_conids(self):
        """从文件加载无效的conid列表"""
        try:
            # 加载无效合约conid
            with self._invalid_conids_lock:
                data = load_json(self.invalid_conids_file)
                self.invalid_conids = set(data) if data else set()
                logger.info(f"已从{self.invalid_conids_file}加载{len(self.invalid_conids)}个无效合约conid")
            
            # 加载无效基本面数据conid
            with self._invalid_snapshot_conids_lock:
                data = load_json(self.invalid_snapshot_conids_file)
                self.invalid_snapshot_conids = set(data) if data else set()
                logger.info(f"已从{self.invalid_snapshot_conids_file}加载{len(self.invalid_snapshot_conids)}个无效基本面数据conid")
                
            # 加载无历史数据conid
            with self._no_head_time_conids_lock:
                data = load_json(self.no_head_time_conids_file)
                self.no_head_time_conids = set(data) if data else set()
                logger.info(f"已从{self.no_head_time_conids_file}加载{len(self.no_head_time_conids)}个无历史数据conid")
                
        except Exception as e:
            logger.error(f"加载无效conid列表时出错: {str(e)}\n{traceback.format_exc()}")
            # 如果加载失败,使用空集合
            self.invalid_conids = set()
            self.invalid_snapshot_conids = set()
            self.no_head_time_conids = set()

    def _save_invalid_conids(self):
        """保存无效的conid列表到文件,需要先读取文件内容再合并"""
        try:
            # 保存无效合约conid
            with self._invalid_conids_lock:
                # 先读取现有内容
                existing_data = load_json(self.invalid_conids_file)
                existing_conids = set(existing_data) if existing_data else set()
                # 合并现有数据和内存中的数据
                merged_conids = existing_conids | self.invalid_conids
                # 保存合并后的数据（排序）
                save_json(self.invalid_conids_file, sorted(list(merged_conids)))
                logger.info(f"已保存{len(merged_conids)}个无效合约conid到{self.invalid_conids_file}")
            
            # 保存无效基本面数据conid
            with self._invalid_snapshot_conids_lock:
                # 先读取现有内容
                existing_data = load_json(self.invalid_snapshot_conids_file)
                existing_snapshot_conids = set(existing_data) if existing_data else set()
                # 合并现有数据和内存中的数据
                merged_snapshot_conids = existing_snapshot_conids | self.invalid_snapshot_conids
                # 保存合并后的数据（排序）
                save_json(self.invalid_snapshot_conids_file, sorted(list(merged_snapshot_conids)))
                logger.info(f"已保存{len(merged_snapshot_conids)}个无效基本面数据conid到{self.invalid_snapshot_conids_file}")
                
            # 保存无历史数据conid
            with self._no_head_time_conids_lock:
                # 先读取现有内容
                existing_data = load_json(self.no_head_time_conids_file)
                existing_no_head_time_conids = set(existing_data) if existing_data else set()
                # 合并现有数据和内存中的数据
                merged_no_head_time_conids = existing_no_head_time_conids | self.no_head_time_conids
                # 保存合并后的数据（排序）
                save_json(self.no_head_time_conids_file, sorted(list(merged_no_head_time_conids)))
                logger.info(f"已保存{len(merged_no_head_time_conids)}个无历史数据conid到{self.no_head_time_conids_file}")
                
        except Exception as e:
            logger.error(f"保存无效conid列表时出错: {str(e)}\n{traceback.format_exc()}")

    def _add_invalid_conid(self, conid: int, is_snapshot: bool = False, is_no_head_time: bool = False):
        """添加一个无效的conid到内存集合中
        
        Args:
            conid: 要添加的conid
            is_snapshot: 是否是无效的基本面数据conid，默认为False表示无效合约
            is_no_head_time: 是否是无历史数据conid，默认为False
        """
        if is_no_head_time:
            with self._no_head_time_conids_lock:
                self.no_head_time_conids.add(conid)
        elif is_snapshot:
            with self._invalid_snapshot_conids_lock:
                self.invalid_snapshot_conids.add(conid)
        else:
            with self._invalid_conids_lock:
                self.invalid_conids.add(conid)

    def _get_earliest_available_date_via_historical_data_sync(self, contract) -> Optional[datetime]:
        """通过历史数据请求获取最早可用日期（精确到日）- 同步版本
        
        Args:
            contract: IB合约对象
            
        Returns:
            最早可用日期，如果没有数据则返回None
        """
        try:
            # 确定whatToShow参数
            whatToShow = "TRADES"
            if hasattr(contract, 'secType'):
                if contract.secType in ('CASH', 'CMDTY'):
                    whatToShow = "MIDPOINT"
                elif contract.exchange == "IDEALPRO":
                    whatToShow = "MIDPOINT"
            
            # 直接请求最大范围的历史数据来获取最早日期
            # 使用30年作为最大范围，这应该覆盖大部分标的的历史
            try:
                bars = self.ib.reqHistoricalData(
                    contract,
                    endDateTime=datetime(2030,1,1),  # 从1970年开始
                    durationStr="10 Y",  # 30年
                    barSizeSetting="1 day",
                    whatToShow=whatToShow,
                    useRTH=True,
                    timeout=60
                )
                
                if bars:
                    earliest_date = bars[0].date
                    logger.info(f"conid={contract.conId}: 通过历史数据找到最早可用日期: {earliest_date}")
                    return earliest_date
                else:
                    logger.warning(f"conid={contract.conId}: 68年范围内没有找到历史数据")
                    return None
                    
            except RequestError as e:
                # if e.code == 162:
                #     # 没有历史数据
                #     logger.warning(f"conid={contract.conId}: 没有历史数据")
                #     return None
                logger.error(f"conid={contract.conId}: 请求历史数据时出错: [{e.code}] {e.message}")
                return None
                
        except Exception as e:
            logger.error(f"conid={contract.conId}: 通过历史数据获取最早日期时出错: {str(e)}\n{traceback.format_exc()}")
            return None

@app.command(name="update-all")
def update_all_command(
    conids: Annotated[Optional[List[int]], typer.Option("--conids", "-c", help="指定要更新的conid列表，不指定则更新所有最新产品conid")] = None
):
    """更新所有合约详情和基本面数据"""
    db_manager.common_db.create_tables([IbContractDetail, ContractTime, IbFundamentals], safe=True)
    updater = IbContractDetailUpdater()
    updater.update_contract_details(conids=set(conids) if conids else None) # typer传入的是list，这里需要转成set
    logger.info("所有合约详情和基本面数据更新完成。")

def _run_head_times_update_task_with_heartbeat(
    port: int,
    conids_batch: List[int],
    heartbeat_queue: Queue,
    stop_event: Event,
    client_id: int,
    use_historical_data: bool = False
):
    """
    负责执行ib_head_time更新的独立任务，带有心跳机制。
    
    Args:
        port: IB网关端口
        conids_batch: 要处理的conid批次
        heartbeat_queue: 心跳队列，用于向主进程发送当前处理的conid
        stop_event: 停止事件
        client_id: 当前使用的客户号
    """
    try:
        db_manager.common_db.create_tables([IbContractDetail, ContractTime, IbFundamentals], safe=True)
        updater = IbContractDetailUpdater()
        updater.connect(port=port, client_id=client_id)
        
        logger.info(f"子进程(端口{port})开始更新ib_head_time，共 {len(conids_batch)} 个conid。")
        
        for conid in conids_batch:
            if stop_event.is_set():
                logger.warning(f"子进程(端口{port})收到停止信号，正在退出...")
                break
                
            try:                
                # 检查是否是已知的无效合约或无历史数据
                if conid in updater.invalid_conids:
                    # 发送心跳，表示移除这个conid
                    heartbeat_queue.put((port, conid, True))
                    logger.info(f"子进程(端口{port}): conid={conid}是已知的无效合约，跳过ib_head_time更新。")
                    continue
                if conid in updater.no_head_time_conids:
                    heartbeat_queue.put((port, conid, True))
                    logger.info(f"子进程(端口{port}): conid={conid}是已知的无历史数据合约，跳过ib_head_time更新。")
                    continue
                
                try:
                    contract = updater._get_qualified_contract(conid)
                except (ConnectionError, asyncio.exceptions.CancelledError):
                    # 连接断开，等待25秒让外层重连
                    logger.warning(f"子进程(端口{port}): 连接断开，等待25秒后重试...")
                    sleep(25)
                    continue
                    
                if not contract:
                    # 如果是其他原因导致获取失败（如无效合约），则标记为处理完成
                    heartbeat_queue.put((port, conid, True))
                    logger.warning(f"子进程(端口{port}): 无conid={conid}的合约，跳过ib_head_time更新。")
                    continue

                # 检查是否为VALUE交易所或primary_exchange为PINK
                if contract.exchange == 'VALUE' or contract.primaryExchange == 'PINK':
                    heartbeat_queue.put((port, conid, True))
                    logger.info(f"子进程(端口{port}): 跳过VALUE交易所或PINK市场标的: conid={conid}")
                    continue

                # 请求head_time
                if use_historical_data:
                    # 使用历史数据请求获取最早可用日期（精确到日）
                    head_time = updater._get_earliest_available_date_via_historical_data_sync(contract)
                else:
                    # 使用传统的reqHeadTimeStamp（精确到秒）
                    head_time = updater.ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)
                
                heartbeat_queue.put((port, conid, True))
                sleep(15)  # 额外等待时间

                if head_time:
                    # 更新数据库
                    with db_manager.common_db.atomic():
                        sql = """
                            INSERT INTO contract_time (conid, ib_head_time)
                            VALUES (%s, %s)
                            ON DUPLICATE KEY UPDATE ib_head_time = VALUES(ib_head_time)
                        """
                        values = (conid, head_time)
                        db_manager.common_db.execute_sql(sql, values)
                    logger.info(f"子进程(端口{port}): 已更新conid={conid}的ib_head_time为: {head_time}")
                else:
                    logger.warning(f"子进程(端口{port}): conid={conid}未找到ib_head_time。")
                    
            except RequestError as e:
                error_msg = e.message.lower()
                if e.code == 162:
                    if any(msg in error_msg for msg in ["no head time stamp", "no route found", "no historical market data"]):
                        # 没有历史数据的情况
                        logger.warning(f"子进程(端口{port}): conid={conid}没有历史数据")
                        updater._add_invalid_conid(conid, is_no_head_time=True)
                        heartbeat_queue.put((port, conid, True))
                    elif "pacing violation" in error_msg:
                        # 请求限流的情况，发送False心跳让主进程重新加回这个conid
                        logger.warning(f"子进程(端口{port}): conid={conid}请求被限流，稍后会重试")
                        heartbeat_queue.put((port, conid, False))
                    else:
                        # 其他162错误
                        logger.error(f"子进程(端口{port}): 更新conid={conid}的ib_head_time时出错: {e.message}")
                        heartbeat_queue.put((port, conid, False))
                else:
                    logger.error(f"子进程(端口{port}): 更新conid={conid}的ib_head_time时出错: [{e.code}] {e.message}")
                    heartbeat_queue.put((port, conid, False))
                sleep(15)  # 额外等待时间
                continue
                    
            except Exception as e:
                logger.error(f"子进程(端口{port}): 更新conid={conid}的ib_head_time时出错: {str(e)}\n{traceback.format_exc()}")
                continue
                
    except Exception as e:
        logger.error(f"子进程(端口{port})发生错误: {str(e)}\n{traceback.format_exc()}")
    finally:
        updater.disconnect()
        logger.info(f"子进程(端口{port})已完成并断开连接。")

@app.command(name="update-head-times")
def update_head_times_command(
    conids_str: Annotated[Optional[str], typer.Option("--conids", "-c", help="指定要更新head_time的conid列表，使用逗号分隔")] = None,
    ports_str: Annotated[Optional[str], typer.Option("--ports", "-p", help="指定要使用的端口列表，用逗号分隔，如 '4002,4012,4022'")] = None,
    base_client_id: Annotated[Optional[int], typer.Option("--base-client-id", "-b", help="指定基础客户号，每个进程会在此基础上递增")] = None,
    use_historical_data: Annotated[bool, typer.Option("--use-historical-data", "-h", help="使用历史数据请求获取最早可用日期（精确到日），而不是reqHeadTimeStamp（精确到秒）")] = False
):
    """仅更新合约的最早交易时间 (ib_head_time)
    调用方式：
    python update_ib_contract_details.py update-head-times --conids 1234567,2345678,3456789 --ports 4002,4012,4022 --base-client-id 611
    python update_ib_contract_details.py update-head-times --ports 4012 --base-client-id 611
    python update_ib_contract_details.py update-head-times --ports 4012 --base-client-id 611 --use-historical-data
    """
    db_manager.common_db.create_tables([IbContractDetail, ContractTime, IbFundamentals], safe=True)
    
    # 解析端口字符串
    ports = [int(p.strip()) for p in ports_str.split(",")] if ports_str else [4002, 4012, 4022]
    
    # 解析conids字符串
    conids = [int(c.strip()) for c in conids_str.split(",")] if conids_str else None
    
    # 获取所有需要处理的conid
    all_conids_to_process = set(conids) if conids else _get_latest_product_conids()
    
    if not all_conids_to_process:
        logger.info("没有指定conid或没有最新的产品conid，跳过head_time更新。")
        return

    # 从ContractTime表中找出所有ib_head_time已存在的conid
    existing_head_times_conids = set()
    query = (
        ContractTime.select(ContractTime.conid)
        .where(ContractTime.ib_head_time.is_null(False))
        .tuples()
    )
    for conid_tuple in query:
        existing_head_times_conids.add(conid_tuple[0])

    # 需要更新的conid是all_conids_to_process中除去已存在ib_head_time的conid
    all_conids_to_process = all_conids_to_process - existing_head_times_conids
    
    if not all_conids_to_process:
        logger.info("所有conid都已有head_time记录，无需更新。")
        return
    
    logger.info(f"开始更新{len(all_conids_to_process)}个conid的head_time...")

    logger.info(f"启动ib_head_time更新服务，使用端口: {ports}")
    
    # 如果没有指定base_client_id，从配置文件读取
    if base_client_id is None:
        connect_filename = 'connect_ib.json'
        setting = load_json(connect_filename)
        base_client_id = setting.get("客户号", 611)
    
    current_client_id = base_client_id + 1  # 当前客户号，会随着进程创建递增
    remaining_conids = all_conids_to_process.copy()  # 待处理的conid集合，移到外部
    
    while remaining_conids:  # 只要还有未处理的conids就继续
        try:
            # 创建共享的队列
            heartbeat_queue = Queue()  # 所有进程共享一个心跳队列
            stop_events = {port: Event() for port in ports}  # 每个端口仍需要独立的停止事件
            last_heartbeat_times = {port: time.time() for port in ports}
            processes = {}
            
            # 将剩余的conid平均分配给各个端口
            conids_list = list(remaining_conids)  # 使用remaining_conids而不是all_conids_to_process
            chunk_size = len(conids_list) // len(ports)
            port_conids = {}
            
            for i, port in enumerate(ports):
                start_idx = i * chunk_size
                end_idx = start_idx + chunk_size if i < len(ports) - 1 else len(conids_list)
                port_conids[port] = conids_list[start_idx:end_idx]
                
                if port_conids[port]:  # 只为有conid要处理的端口创建进程
                    logger.info(f"为端口{port}分配{len(port_conids[port])}个conid处理，使用客户号{current_client_id}")
                    process = Process(
                        target=_run_head_times_update_task_with_heartbeat,
                        args=(
                            port,
                            port_conids[port],
                            heartbeat_queue,  # 共享的心跳队列
                            stop_events[port],
                            current_client_id,
                            use_historical_data
                        )
                    )
                    processes[port] = process
                    process.start()
                    logger.info(f"端口{port}的子进程已启动。")
                    current_client_id += 1  # 为下一个进程递增客户号
            
            # 监控子进程的心跳和处理进度
            while remaining_conids and any(p.is_alive() for p in processes.values()):
                # 检查心跳
                try:
                    while True:  # 处理队列中的所有心跳
                        port_num, conid, not_anymore = heartbeat_queue.get_nowait()
                        last_heartbeat_times[port_num] = time.time()
                        if not_anymore:  # 开始处理时从remaining_conids中移除
                            if conid in remaining_conids:
                                remaining_conids.remove(conid)
                                logger.info(f"端口{port_num}处理conid={conid}完成，还剩{len(remaining_conids)}个待处理。")
                        else:  # 遇到限流时将conid加回remaining_conids
                            remaining_conids.add(conid)
                            logger.info(f"端口{port_num}处理conid={conid}重新加入待处理队列。")
                except queue.Empty:
                    pass
                
                # 检查心跳超时
                for port in ports:
                    if port not in processes:
                        continue
                        
                    if time.time() - last_heartbeat_times[port] > 20:
                        logger.warning(f"端口{port}的子进程心跳超时，准备重启...")
                        stop_events[port].set()  # 通知子进程停止
                        processes[port].join(timeout=5)  # 等待子进程结束
                        if processes[port].is_alive():
                            processes[port].terminate()
                        
                        # 重新分配未处理的conid
                        remaining_port_conids = [c for c in port_conids[port] if c in remaining_conids]
                        if remaining_port_conids:
                            logger.info(f"重新启动端口{port}的子进程，处理剩余{len(remaining_port_conids)}个conid，使用客户号{current_client_id}")
                            stop_events[port] = Event()
                            process = Process(
                                target=_run_head_times_update_task_with_heartbeat,
                                args=(
                                    port,
                                    remaining_port_conids,
                                    heartbeat_queue,  # 使用同一个共享队列
                                    stop_events[port],
                                    current_client_id,
                                    use_historical_data
                                )
                            )
                            processes[port] = process
                            process.start()
                            last_heartbeat_times[port] = time.time()
                            current_client_id += 1  # 为下一个可能的进程递增客户号
                
                time.sleep(1)  # 避免过于频繁的检查
            
            # 清理所有进程
            for port in ports:
                if port in processes:
                    stop_events[port].set()
                    processes[port].join(timeout=5)
                    if processes[port].is_alive():
                        processes[port].terminate()
            
            logger.info(f"本轮更新完成，还剩{len(remaining_conids)}个conid未处理。")
            
            if remaining_conids:
                # 等待一段时间后开始下一轮更新
                logger.info("等待2秒后开始下一轮更新...")
                time.sleep(2)
            
        except Exception as e:
            logger.error(f"主进程发生错误: {str(e)}\n{traceback.format_exc()}")
            # 清理所有进程
            for port in ports:
                if port in processes:
                    stop_events[port].set()
                    processes[port].join(timeout=5)
                    if processes[port].is_alive():
                        processes[port].terminate()
            time.sleep(5)  # 发生错误后等待较长时间再重试
            
    logger.info("所有conid处理完成。")

@app.command(name="update-report-snapshots")
def update_report_snapshots_command(
    conids: Annotated[Optional[List[int]], typer.Option("--conids", "-c", help="指定要更新ReportSnapshot的conid列表，不指定则更新所有最新产品conid")] = None
):
    """仅更新合约的基本面数据 (ReportSnapshot)"""
    db_manager.common_db.create_tables([IbContractDetail, ContractTime, IbFundamentals], safe=True)
    updater = IbContractDetailUpdater()
    updater.connect()
    conids_to_process = set(conids) if conids else updater.latest_product_conids
    if not conids_to_process:
        logger.info("没有指定conid或没有最新的产品conid，跳过ReportSnapshot更新。")
        return

    fundamentals_queue = queue.Queue()
    fundamentals_fetching_completed = threading.Event()

    fundamentals_save_thread = threading.Thread(
        target=db_manager.process_and_save_queue,
        args=(fundamentals_queue, fundamentals_fetching_completed, IbFundamentals)
    )
    fundamentals_save_thread.start()

    updater._update_report_snapshots(conids_to_process, fundamentals_queue, fundamentals_fetching_completed)

    fundamentals_save_thread.join()
    updater.disconnect()
    logger.info("ReportSnapshot数据更新完成。")

def merge_contract_time_data(old_conid: int, new_conid: int, old_symbol: str, new_symbol: str):
    """合并两个conid的ContractTime数据，取较小的非null值
    
    Args:
        old_conid: 旧conid
        new_conid: 新conid
        old_symbol: 旧symbol
        new_symbol: 新symbol
    """
    # 获取旧conid和新conid的记录
    old_record = None
    new_record = None
    
    try:
        old_record = ContractTime.get(ContractTime.conid == old_conid)
    except ContractTime.DoesNotExist:
        pass
    
    try:
        new_record = ContractTime.get(ContractTime.conid == new_conid)
    except ContractTime.DoesNotExist:
        pass
    
    if not old_record:
        logger.info(f"旧conid {old_conid} 在ContractTime表中不存在记录")
        return
    
    # 需要合并的时间字段
    time_fields = ['ib_head_time', 'wind_ipo_date', 'yahoo_first_trade_date_milliseconds', 'futu_list_time']
    
    # 如果新记录不存在，直接更新conid
    if not new_record:
        ContractTime.update(conid=new_conid).where(ContractTime.conid == old_conid).execute()
        logger.info(f"合并ContractTime: {old_conid} ({old_symbol}) -> {new_conid} ({new_symbol}), 直接更新conid")
        return
    
    # 合并数据：对于每个字段，如果都非null则取较小值，否则取非null值
    merged_data = {}
    has_updates = False
    
    for field in time_fields:
        old_value = getattr(old_record, field)
        new_value = getattr(new_record, field)
        
        if old_value is not None and new_value is not None:
            # 都非null，取较小值
            merged_value = min(old_value, new_value)
            if merged_value != new_value:
                merged_data[field] = merged_value
                has_updates = True
                logger.info(f"字段 {field}: 取较小值 {merged_value} (旧:{old_value}, 新:{new_value})")
        elif old_value is not None and new_value is None:
            # 只有旧值非null，使用旧值
            merged_data[field] = old_value
            has_updates = True
            logger.info(f"字段 {field}: 使用旧值 {old_value}")
        # 如果新值非null而旧值null，或者都是null，保持新值不变
    
    # 更新新记录
    if has_updates:
        ContractTime.update(**merged_data).where(ContractTime.conid == new_conid).execute()
        logger.info(f"合并ContractTime数据: {old_conid} ({old_symbol}) -> {new_conid} ({new_symbol})")
    
    # 删除旧记录
    ContractTime.delete().where(ContractTime.conid == old_conid).execute()
    logger.info(f"删除旧ContractTime记录: {old_conid} ({old_symbol})")

def update_contract_time_conids(conid_mappings: Dict[int, dict]):
    """更新ContractTime表中的conid
    
    Args:
        conid_mappings: conid映射字典，格式为 {old_conid: {'new_conid': new_conid, 'old_symbol': old_symbol, 'new_symbol': new_symbol}}
    """
    if not conid_mappings:
        logger.info("没有需要更新的conid")
        return
        
    logger.info(f"开始更新ContractTime表中的{len(conid_mappings)}个conid...")
    
    with db_manager.common_db.atomic():
        for old_conid, mapping_info in conid_mappings.items():
            new_conid = mapping_info['new_conid']
            old_symbol = mapping_info['old_symbol']
            new_symbol = mapping_info['new_symbol']
            
            # 更新ContractTime表中的conid
            updated_rows = (ContractTime
                          .update(conid=new_conid)
                          .where(ContractTime.conid == old_conid)
                          .execute())
            
            if updated_rows > 0:
                logger.info(f"更新ContractTime: {old_conid} ({old_symbol}) -> {new_conid} ({new_symbol}), 影响{updated_rows}行")

def handle_warning_mappings(warning_mappings: Dict[int, dict]):
    """处理warning_mappings中的conid，合并时间信息并删除旧记录
    
    Args:
        warning_mappings: 警告映射字典，格式为 {old_conid: {'new_conid': new_conid, 'old_symbol': old_symbol, 'new_symbol': new_symbol}}
    """
    if not warning_mappings:
        logger.info("没有需要处理的warning映射")
        return
        
    logger.info(f"开始处理{len(warning_mappings)}个warning映射的conid...")
    
    with db_manager.common_db.atomic():
        for old_conid, mapping_info in warning_mappings.items():
            new_conid = mapping_info['new_conid']
            old_symbol = mapping_info['old_symbol']
            new_symbol = mapping_info['new_symbol']
            
            merge_contract_time_data(old_conid, new_conid, old_symbol, new_symbol)

@app.command(name="update-contract-time-conids")
def update_contract_time_conids_command():
    """更新ContractTime表中的旧conid为新conid"""
    try:
        db_manager.common_db.create_tables([ContractTime], safe=True)
        
        # 1. 从ContractTime表获取所有conid
        old_conids = set()
        query = ContractTime.select(ContractTime.conid).tuples()
        for conid_tuple in query:
            if conid_tuple[0]:
                old_conids.add(conid_tuple[0])
        
        if not old_conids:
            logger.info("ContractTime表中没有conid数据")
            return
            
        logger.info(f"从ContractTime表获取到{len(old_conids)}个conid")
        
        # 2. 获取conid映射关系
        logger.info("正在获取conid映射关系...")
        conid_mappings, warning_mappings = get_conid_mapping(old_conids)
        
        # 3. 输出警告信息
        if warning_mappings:
            logger.warning(f"发现{len(warning_mappings)}个链式判断错误的映射关系，这些映射将被跳过:")
            for old_conid, mapping_info in warning_mappings.items():
                logger.warning(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                             f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
        
        # 4. 输出需要更新的conid信息
        if conid_mappings:
            logger.info(f"找到{len(conid_mappings)}个需要更新的conid:")
            for old_conid, mapping_info in conid_mappings.items():
                logger.info(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                           f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
        else:
            logger.info("没有需要更新的conid")
        
        # 5. 更新ContractTime表
        update_contract_time_conids(conid_mappings)
        
        # 6. 处理warning映射
        handle_warning_mappings(warning_mappings)
        
        logger.info("ContractTime表conid更新完成")
        
    except Exception as e:
        logger.error(f"更新ContractTime表conid时发生错误: {str(e)}\n{traceback.format_exc()}")
        raise

if __name__ == "__main__":
    # 动态生成调度器日志文件名，并添加一个过滤器，只处理来自本模块的日志
    log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
    logger.add(
        f"logs/{log_file_name}",
        level=0,
        format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="00:00",
        filter=__name__
    )
    app()