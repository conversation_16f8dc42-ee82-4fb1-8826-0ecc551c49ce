-- SQL脚本：清理并复制数据（改进版 - 避免长字符串拼接问题）
-- 目标：从vnpy_stk_us_ib_m_2206_250814bak库的dbbaroverview中找带_的symbol，
-- 删除对应的数据，然后从vnpy_stk_us_ib_m_2206_250814库复制数据过来

-- 首先创建永久表存储需要处理的基础symbol
DROP TABLE IF EXISTS temp_test_symbols;
CREATE TABLE temp_test_symbols AS
SELECT DISTINCT SUBSTRING_INDEX(symbol, '_', 1) as base_symbol
FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE symbol LIKE '%\_%' ESCAPE '\\';

-- 创建索引提高性能
CREATE INDEX idx_base_symbol ON temp_test_symbols(base_symbol);

-- 删除dbbardata中的数据（使用EXISTS避免长字符串拼接）
DELETE FROM vnpy_stk_us_ib_m_2206_250814bak.dbbardata
WHERE EXISTS (
    SELECT 1 FROM temp_test_symbols t
    WHERE vnpy_stk_us_ib_m_2206_250814bak.dbbardata.symbol = t.base_symbol
       OR vnpy_stk_us_ib_m_2206_250814bak.dbbardata.symbol LIKE CONCAT(t.base_symbol, '\_%') ESCAPE '\\'
);

-- 删除dbbaroverview中的数据
DELETE FROM vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview
WHERE EXISTS (
    SELECT 1 FROM temp_test_symbols t
    WHERE vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview.symbol = t.base_symbol
       OR vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview.symbol LIKE CONCAT(t.base_symbol, '\_%') ESCAPE '\\'
);

-- 从源库复制dbbardata数据（使用EXISTS）
INSERT INTO vnpy_stk_us_ib_m_2206_250814bak.dbbardata
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_ib_m_2206_250814.dbbardata
WHERE EXISTS (
    SELECT 1 FROM temp_test_symbols t
    WHERE vnpy_stk_us_ib_m_2206_250814.dbbardata.symbol = t.base_symbol
       OR vnpy_stk_us_ib_m_2206_250814.dbbardata.symbol LIKE CONCAT(t.base_symbol, '\_%') ESCAPE '\\'
)
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

-- 重新生成overview数据
INSERT INTO vnpy_stk_us_ib_m_2206_250814bak.dbbaroverview (symbol, exchange, `interval`, `count`, `start`, `end`)
SELECT
    symbol,
    exchange,
    `interval`,
    COUNT(id) AS `count`,
    MIN(datetime) AS `start`,
    MAX(datetime) AS `end`
FROM
    vnpy_stk_us_ib_m_2206_250814bak.dbbardata
WHERE EXISTS (
    SELECT 1 FROM temp_test_symbols t
    WHERE vnpy_stk_us_ib_m_2206_250814bak.dbbardata.symbol = t.base_symbol
       OR vnpy_stk_us_ib_m_2206_250814bak.dbbardata.symbol LIKE CONCAT(t.base_symbol, '\_%') ESCAPE '\\'
)
GROUP BY
    symbol, exchange, `interval`
ON DUPLICATE KEY UPDATE
    `count` = VALUES(`count`),
    `start` = VALUES(`start`),
    `end` = VALUES(`end`);

-- 清理永久表
DROP TABLE temp_test_symbols;

-- 使用说明：
-- 1. 此脚本使用EXISTS和LIKE避免了长字符串拼接的问题
-- 2. 对于每个基础symbol，会匹配：
--    - 精确匹配基础symbol本身（如：265598）
--    - 匹配以基础symbol_开头的所有变体（如：265598_240601, 265598_230415等）
-- 3. 性能更好，不受symbol数量限制
-- 4. 自动处理所有相关数据的删除和复制