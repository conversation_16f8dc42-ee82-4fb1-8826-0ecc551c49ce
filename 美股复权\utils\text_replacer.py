#!/usr/bin/env python3
"""
替换当前文件夹及子文件夹中所有.py文件中的指定文本
对每个文件需要用户确认是否替换
"""

import os
import sys
from pathlib import Path
import typer


def find_files_by_extension(directory, extension):
    """递归查找指定扩展名的所有文件"""
    target_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(extension):
                target_files.append(os.path.join(root, file))
    return target_files


def find_occurrences(file_path, search_text):
    """查找文件中包含搜索文本的所有行"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        occurrences = []
        for line_num, line in enumerate(lines, 1):
            if search_text in line:
                occurrences.append((line_num, line.rstrip()))
        
        return occurrences
    except Exception as e:
        typer.echo(f"读取文件 {file_path} 时出错: {e}", err=True)
        return []


def replace_in_file(file_path, old_text, new_text):
    """替换文件中的文本"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        new_content = content.replace(old_text, new_text)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        return True
    except Exception as e:
        typer.echo(f"处理文件 {file_path} 时出错: {e}", err=True)
        return False


app = typer.Typer()

@app.command()
def main(
    old_text: str = typer.Argument("stable_id", help="要替换的原文本"),
    new_text: str = typer.Argument("group_id", help="替换后的新文本"),
    directory: str = typer.Option(".", "--dir", "-d", help="搜索目录路径，默认为当前目录"),
    file_extension: str = typer.Option(".py", "--ext", "-e", help="文件扩展名，默认为.py")
):
    """
    在指定目录及子目录中替换Python文件中的文本
    
    对每个包含目标文本的文件会显示所有匹配行，并询问用户是否替换
    """
    current_dir = os.path.abspath(directory)
    
    if not os.path.exists(current_dir):
        typer.echo(f"错误: 目录 '{directory}' 不存在", err=True)
        raise typer.Exit(1)
    
    typer.echo(f"正在搜索目录: {current_dir}")
    typer.echo(f"文件类型: *{file_extension}")
    typer.echo(f"将替换: '{old_text}' -> '{new_text}'")
    typer.echo("-" * 50)
    
    # 查找所有目标文件
    target_files = find_files_by_extension(current_dir, file_extension)
    
    if not target_files:
        typer.echo(f"未找到任何 *{file_extension} 文件")
        return
    
    typer.echo(f"找到 {len(target_files)} 个 *{file_extension} 文件")
    
    # 查找包含目标文本的文件
    files_with_target = []
    for file_path in target_files:
        occurrences = find_occurrences(file_path, old_text)
        if occurrences:
            files_with_target.append((file_path, occurrences))
    
    if not files_with_target:
        typer.echo(f"没有文件包含 '{old_text}' (区分大小写)")
        return
    
    typer.echo(f"\n找到 {len(files_with_target)} 个文件包含 '{old_text}' (区分大小写):")
    for file_path, occurrences in files_with_target:
        rel_path = os.path.relpath(file_path, current_dir)
        typer.echo(f"  {rel_path}: {len(occurrences)} 次")
    
    typer.echo("\n" + "=" * 50)
    
    # 逐个文件询问是否替换
    replaced_count = 0
    for file_path, occurrences in files_with_target:
        rel_path = os.path.relpath(file_path, current_dir)
        
        typer.echo(f"\n文件: {rel_path}")
        typer.echo(f"包含 '{old_text}' {len(occurrences)} 次:")
        typer.echo("-" * 40)
        
        # 显示所有包含目标文本的行
        for line_num, line_content in occurrences:
            typer.echo(f"  行 {line_num:3d}: {line_content}")
        
        typer.echo("-" * 40)
        
        while True:
            response = typer.prompt("是否替换此文件? (y/n/q)", default="n").lower().strip()
            if response in ['y', 'yes']:
                if replace_in_file(file_path, old_text, new_text):
                    typer.echo(f"✓ 已替换 {rel_path}")
                    replaced_count += 1
                else:
                    typer.echo(f"✗ 替换失败 {rel_path}")
                break
            elif response in ['n', 'no']:
                typer.echo(f"跳过 {rel_path}")
                break
            elif response in ['q', 'quit']:
                typer.echo("用户退出")
                raise typer.Exit(0)
            else:
                typer.echo("请输入 y(是), n(否), 或 q(退出)")
    
    typer.echo(f"\n" + "=" * 50)
    typer.echo(f"完成! 共处理了 {replaced_count} 个文件")


if __name__ == "__main__":
    try:
        app()
    except KeyboardInterrupt:
        typer.echo("\n用户中断操作")
        raise typer.Exit(0)