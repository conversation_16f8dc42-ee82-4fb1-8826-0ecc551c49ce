#!/usr/bin/env python3
"""
从ReportSnapshot XML文件中提取shares_per_listing字段并更新到数据库

使用方法:
python update_shares_per_listing_from_xml.py --xml-dir "ReportSnapshot 20250801"
"""

import traceback
import os
import sys
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import List, Dict, Optional
import typer
from typing_extensions import Annotated
from loguru import logger

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, IbFundamentals

app = typer.Typer()

def parse_shares_per_listing_from_xml(xml_content: str, conid: int) -> Optional[Dict[str, any]]:
    """解析XML文件提取shares_per_listing和最近拆股信息
    
    Args:
        xml_content: XML内容字符串
        conid: 合约ID
        
    Returns:
        包含conid、shares_per_listing、most_recent_split_date、most_recent_split_ratio的字典，如果解析失败返回None
    """
    try:
        root = ET.fromstring(xml_content)
        
        data = {"conid": conid}
        
        # 解析GlobalListingType中的SharesPerListing
        # 查找Issues下的Issue元素中的GlobalListingType
        issues_elem = root.find(".//Issues")
        if issues_elem:
            for issue_elem in issues_elem.findall("Issue"):
                global_listing_type_elem = issue_elem.find("GlobalListingType")
                if global_listing_type_elem is not None:
                    shares_per_listing_attr = global_listing_type_elem.get("SharesPerListing")
                    if shares_per_listing_attr:
                        try:
                            shares_per_listing_value = float(shares_per_listing_attr)
                            logger.info(f"conid={conid}: 找到shares_per_listing={shares_per_listing_value}")
                            data["shares_per_listing"] = shares_per_listing_value
                        except (ValueError, TypeError):
                            logger.warning(f"conid={conid}: shares_per_listing值无法转换为数值: {shares_per_listing_attr}")
                            data["shares_per_listing"] = None
                    else:
                        logger.debug(f"conid={conid}: SharesPerListing属性为空")
                        data["shares_per_listing"] = None
                
                # 解析MostRecentSplit信息
                most_recent_split_elem = issue_elem.find("MostRecentSplit")
                if most_recent_split_elem is not None:
                    # 解析拆股日期
                    split_date_str = most_recent_split_elem.get("Date")
                    if split_date_str:
                        try:
                            from datetime import datetime
                            split_date = datetime.strptime(split_date_str, "%Y-%m-%d")
                            data["most_recent_split_date"] = split_date
                            logger.info(f"conid={conid}: 找到拆股日期={split_date}")
                        except (ValueError, TypeError):
                            logger.warning(f"conid={conid}: 拆股日期无法解析: {split_date_str}")
                            data["most_recent_split_date"] = None
                    else:
                        data["most_recent_split_date"] = None
                    
                    # 解析拆股比例
                    split_ratio_str = most_recent_split_elem.text
                    if split_ratio_str:
                        try:
                            split_ratio = float(split_ratio_str)
                            data["most_recent_split_ratio"] = split_ratio
                            logger.info(f"conid={conid}: 找到拆股比例={split_ratio}")
                        except (ValueError, TypeError):
                            logger.warning(f"conid={conid}: 拆股比例无法转换为数值: {split_ratio_str}")
                            data["most_recent_split_ratio"] = None
                    else:
                        data["most_recent_split_ratio"] = None
                else:
                    logger.debug(f"conid={conid}: 未找到MostRecentSplit元素")
                    data["most_recent_split_date"] = None
                    data["most_recent_split_ratio"] = None
                
                break  # 只取第一个Issue的数据
        
        return data
        
    except ET.ParseError as e:
        logger.error(f"解析conid={conid}的XML时出错: {e}")
        return None
    except Exception as e:
        logger.error(f"处理conid={conid}的XML时发生未知错误: {str(e)}\n{traceback.format_exc()}")
        return None

def scan_xml_files(xml_dir: str) -> List[Dict[str, any]]:
    """扫描XML目录并解析所有文件
    
    Args:
        xml_dir: XML文件目录路径
        
    Returns:
        解析结果列表
    """
    xml_dir_path = Path(xml_dir)
    if not xml_dir_path.exists():
        logger.error(f"XML目录不存在: {xml_dir}")
        return []
    
    if not xml_dir_path.is_dir():
        logger.error(f"指定路径不是目录: {xml_dir}")
        return []
    
    xml_files = list(xml_dir_path.glob("*.xml"))
    if not xml_files:
        logger.warning(f"在目录 {xml_dir} 中未找到XML文件")
        return []
    
    logger.info(f"在目录 {xml_dir} 中找到 {len(xml_files)} 个XML文件")
    
    results = []
    processed_count = 0
    error_count = 0
    
    for xml_file in xml_files:
        try:
            # 从文件名提取conid
            filename = xml_file.stem  # 不包含扩展名的文件名
            try:
                conid = int(filename)
            except ValueError:
                logger.warning(f"无法从文件名提取conid: {xml_file.name}")
                error_count += 1
                continue
            
            # 读取XML文件内容
            try:
                with open(xml_file, 'r', encoding='utf-8') as f:
                    xml_content = f.read()
            except Exception as e:
                logger.error(f"读取XML文件失败 {xml_file}: {e}")
                error_count += 1
                continue
            
            # 解析XML内容
            result = parse_shares_per_listing_from_xml(xml_content, conid)
            if result is not None:
                results.append(result)
                processed_count += 1
            else:
                error_count += 1
                
        except Exception as e:
            logger.error(f"处理文件 {xml_file} 时发生错误: {str(e)}\n{traceback.format_exc()}")
            error_count += 1
            continue
    
    logger.info(f"XML文件处理完成: 成功处理 {processed_count} 个，错误 {error_count} 个")
    return results

def update_fundamentals_to_database(data_list: List[Dict[str, any]]) -> None:
    """将基本面数据更新到数据库
    
    Args:
        data_list: 包含conid、shares_per_listing、most_recent_split_date、most_recent_split_ratio的数据列表
    """
    if not data_list:
        logger.info("没有数据需要更新到数据库")
        return
    
    try:
        # 统计有效数据
        shares_per_listing_count = 0
        split_date_count = 0
        split_ratio_count = 0
        
        for data in data_list:
            if data.get("shares_per_listing") is not None:
                shares_per_listing_count += 1
            if data.get("most_recent_split_date") is not None:
                split_date_count += 1
            if data.get("most_recent_split_ratio") is not None:
                split_ratio_count += 1
        
        logger.info(f"准备更新 {len(data_list)} 条记录到数据库")
        logger.info(f"  其中 {shares_per_listing_count} 条有shares_per_listing值")
        logger.info(f"  其中 {split_date_count} 条有拆股日期")
        logger.info(f"  其中 {split_ratio_count} 条有拆股比例")
        
        # 构建SQL更新语句
        if data_list:
            with db_manager.common_db.atomic():
                for data in data_list:
                    conid = data["conid"]
                    shares_per_listing = data.get("shares_per_listing")
                    most_recent_split_date = data.get("most_recent_split_date")
                    most_recent_split_ratio = data.get("most_recent_split_ratio")
                    
                    # 更新基本面数据字段
                    sql = """
                        UPDATE ib_fundamentals 
                        SET shares_per_listing = %s, 
                            most_recent_split_date = %s,
                            most_recent_split_ratio = %s,
                            update_time = CURRENT_TIMESTAMP
                        WHERE conid = %s
                    """
                    
                    cursor = db_manager.common_db.execute_sql(sql, (shares_per_listing, most_recent_split_date, most_recent_split_ratio, conid))
                    affected_rows = cursor.rowcount
                    
                    if affected_rows == 1:
                        logger.debug(f"成功更新conid={conid}的基本面数据")
                    elif affected_rows == 0:
                        logger.warning(f"conid={conid}在ib_fundamentals表中不存在，跳过更新")
                    else:
                        logger.warning(f"conid={conid}更新影响了{affected_rows}行（预期1行）")
        
        logger.info(f"基本面数据字段更新完成")
        
    except Exception as e:
        logger.error(f"更新数据库时发生错误: {str(e)}\n{traceback.format_exc()}")
        raise

@app.command()
def update_from_xml_dir(
    xml_dir: Annotated[str, typer.Option("--xml-dir", "-d", help="包含XML文件的目录路径")] = "ReportSnapshot 20250801"
):
    """从XML目录中提取shares_per_listing字段并更新到数据库"""
    try:
        # 确保数据库表存在
        db_manager.common_db.create_tables([IbFundamentals], safe=True)
        
        logger.info(f"开始处理XML目录: {xml_dir}")
        
        # 扫描并解析XML文件
        xml_results = scan_xml_files(xml_dir)
        
        if not xml_results:
            logger.warning("没有成功解析任何XML文件")
            return
        
        # 更新到数据库
        update_fundamentals_to_database(xml_results)
        
        logger.info("基本面数据字段更新任务完成")
        
    except Exception as e:
        logger.error(f"执行更新任务时发生错误: {str(e)}\n{traceback.format_exc()}")
        raise

if __name__ == "__main__":
    # 设置日志
    log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
    logger.add(
        f"logs/{log_file_name}",
        level=0,
        format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="00:00",
        filter=__name__
    )
    
    app()
