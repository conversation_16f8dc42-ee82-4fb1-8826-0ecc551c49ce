#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库清理和复制工具
删除vnpy_stk_us_ib_m_2206_250814bak中symbol带_的记录，
然后从vnpy_stk_us_ib_m_2206_250814复制temp_test_symbols中的数据
"""

import os
import sys
import time
from typing import List, Set
from datetime import datetime

from loguru import logger

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from vnpy.trader.setting import SETTINGS
from utils.mysql_database import create_mysql_database

# 配置日志
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level="INFO",
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00"
)

class DatabaseCleanupCopier:
    """数据库清理和复制工具"""
    
    def __init__(self, source_db: str, target_db: str):
        """
        初始化
        Args:
            source_db: 源数据库名称 vnpy_stk_us_ib_m_2206_250814
            target_db: 目标数据库名称 vnpy_stk_us_ib_m_2206_250814bak
        """
        self.source_db = source_db
        self.target_db = target_db
        
        # 创建数据库连接
        source_settings = SETTINGS.copy()
        source_settings['database.database'] = source_db
        
        target_settings = SETTINGS.copy()
        target_settings['database.database'] = target_db
        
        logger.info(f"连接源数据库: {source_db}")
        self.source_database, self.SourceDbBarData, _, self.SourceDbBarOverview, _ = create_mysql_database(
            source_settings, replace_on_conflict=True
        )
        
        logger.info(f"连接目标数据库: {target_db}")
        self.target_database, self.TargetDbBarData, _, self.TargetDbBarOverview, _ = create_mysql_database(
            target_settings, replace_on_conflict=True
        )
        
        logger.info("数据库连接初始化完成")
        
    def get_symbols_with_underscore(self) -> Set[str]:
        """获取目标数据库中symbol带_的所有记录"""
        logger.info("查找目标数据库中symbol带_的记录...")
        
        query = self.TargetDbBarOverview.select(self.TargetDbBarOverview.symbol).where(
            self.TargetDbBarOverview.symbol.contains('_')
        ).distinct()
        
        symbols_with_underscore = set()
        for row in query:
            symbols_with_underscore.add(row.symbol)
            
        logger.info(f"找到 {len(symbols_with_underscore)} 个带_的symbol")
        return symbols_with_underscore
        
    def get_base_symbols_from_underscore(self, symbols_with_underscore: Set[str]) -> Set[str]:
        """从带_的symbol中提取基础symbol（_前面的部分）"""
        base_symbols = set()
        for symbol in symbols_with_underscore:
            base_symbol = symbol.split('_')[0]
            base_symbols.add(base_symbol)
        
        logger.info(f"提取到 {len(base_symbols)} 个基础symbol")
        return base_symbols
        
    def get_temp_test_symbols(self) -> Set[str]:
        """从temp_test_symbols表获取需要复制的symbol列表"""
        try:
            # 尝试直接查询temp_test_symbols表
            sql = "SELECT DISTINCT symbol FROM temp_test_symbols"
            cursor = self.source_database.db.execute_sql(sql)
            symbols = {row[0] for row in cursor.fetchall()}
            logger.info(f"从temp_test_symbols表获取到 {len(symbols)} 个symbol")
            return symbols
        except Exception as e:
            logger.error(f"无法查询temp_test_symbols表: {e}")
            return set()
            
    def delete_records_by_batch(self, base_symbols: Set[str], batch_size: int = 200000):
        """分批删除目标数据库中的相关记录"""
        logger.info(f"开始分批删除记录，批量大小: {batch_size}")
        
        # 删除dbbardata中的记录
        total_deleted_data = 0
        for base_symbol in base_symbols:
            while True:
                # 构建删除条件：精确匹配base_symbol或以base_symbol_开头
                delete_query = self.TargetDbBarData.delete().where(
                    (self.TargetDbBarData.symbol == base_symbol) |
                    (self.TargetDbBarData.symbol.startswith(f"{base_symbol}_"))
                ).limit(batch_size)
                
                deleted_count = delete_query.execute()
                total_deleted_data += deleted_count
                
                logger.info(f"删除symbol {base_symbol} 相关的 {deleted_count} 条dbbardata记录")
                
                if deleted_count < batch_size:
                    break  # 该symbol的所有记录已删除完毕
                    
                time.sleep(0.1)  # 短暂休息避免数据库压力过大
                
        # 删除dbbaroverview中的记录
        total_deleted_overview = 0
        for base_symbol in base_symbols:
            delete_query = self.TargetDbBarOverview.delete().where(
                (self.TargetDbBarOverview.symbol == base_symbol) |
                (self.TargetDbBarOverview.symbol.startswith(f"{base_symbol}_"))
            )
            
            deleted_count = delete_query.execute()
            total_deleted_overview += deleted_count
            logger.info(f"删除symbol {base_symbol} 相关的 {deleted_count} 条dbbaroverview记录")
            
        logger.info(f"删除完成 - 共删除 {total_deleted_data} 条dbbardata记录，{total_deleted_overview} 条dbbaroverview记录")
        
    def copy_symbols_by_batch(self, symbols_to_copy: Set[str], symbols_per_batch: int = 10):
        """分批复制symbol数据"""
        logger.info(f"开始分批复制数据，每批处理 {symbols_per_batch} 个symbol")
        
        symbols_list = list(symbols_to_copy)
        total_symbols = len(symbols_list)
        
        for i in range(0, total_symbols, symbols_per_batch):
            batch_symbols = symbols_list[i:i + symbols_per_batch]
            logger.info(f"处理批次 {i//symbols_per_batch + 1}，symbol数量: {len(batch_symbols)}")
            
            for symbol in batch_symbols:
                self._copy_single_symbol(symbol)
                time.sleep(0.05)  # 短暂休息
                
        logger.info("所有symbol数据复制完成")
        
    def _copy_single_symbol(self, symbol: str):
        """复制单个symbol的所有相关数据"""
        try:
            # 查询源数据库中该symbol的所有相关记录（精确匹配和以symbol_开头的）
            source_query = self.SourceDbBarData.select().where(
                (self.SourceDbBarData.symbol == symbol) |
                (self.SourceDbBarData.symbol.startswith(f"{symbol}_"))
            )
            
            # 批量插入到目标数据库
            batch_data = []
            record_count = 0
            
            for record in source_query:
                batch_data.append({
                    'symbol': record.symbol,
                    'exchange': record.exchange,
                    'datetime': record.datetime,
                    'interval': record.interval,
                    'volume': record.volume,
                    'turnover': record.turnover,
                    'open_interest': record.open_interest,
                    'open_price': record.open_price,
                    'high_price': record.high_price,
                    'low_price': record.low_price,
                    'close_price': record.close_price
                })
                record_count += 1
                
                # 每1000条记录执行一次插入
                if len(batch_data) >= 1000:
                    self._insert_batch_data(batch_data)
                    batch_data = []
                    
            # 插入剩余数据
            if batch_data:
                self._insert_batch_data(batch_data)
                
            logger.info(f"复制symbol {symbol}: {record_count} 条记录")
            
        except Exception as e:
            logger.error(f"复制symbol {symbol} 时出错: {e}")
            
    def _insert_batch_data(self, batch_data: List[dict]):
        """批量插入数据到目标数据库"""
        if not batch_data:
            return
            
        try:
            with self.target_database.db.atomic():
                query = self.TargetDbBarData.insert_many(batch_data).on_conflict_replace()
                query.execute()
        except Exception as e:
            logger.error(f"批量插入数据时出错: {e}")
            
    def regenerate_overview(self, symbols: Set[str]):
        """重新生成overview数据"""
        logger.info("重新生成overview数据...")
        
        for symbol in symbols:
            try:
                # 查询该symbol相关的所有记录统计信息
                query = self.TargetDbBarData.select().where(
                    (self.TargetDbBarData.symbol == symbol) |
                    (self.TargetDbBarData.symbol.startswith(f"{symbol}_"))
                )
                
                # 按symbol, exchange, interval分组统计
                symbol_stats = {}
                for record in query:
                    key = (record.symbol, record.exchange, record.interval)
                    if key not in symbol_stats:
                        symbol_stats[key] = {
                            'count': 0,
                            'start': record.datetime,
                            'end': record.datetime
                        }
                    
                    symbol_stats[key]['count'] += 1
                    if record.datetime < symbol_stats[key]['start']:
                        symbol_stats[key]['start'] = record.datetime
                    if record.datetime > symbol_stats[key]['end']:
                        symbol_stats[key]['end'] = record.datetime
                
                # 插入或更新overview记录
                for (sym, exchange, interval), stats in symbol_stats.items():
                    overview_data = {
                        'symbol': sym,
                        'exchange': exchange,
                        'interval': interval,
                        'count': stats['count'],
                        'start': stats['start'],
                        'end': stats['end']
                    }
                    
                    query = self.TargetDbBarOverview.insert(overview_data).on_conflict_replace()
                    query.execute()
                    
                logger.debug(f"重新生成symbol {symbol} 的 {len(symbol_stats)} 条overview记录")
                
            except Exception as e:
                logger.error(f"重新生成symbol {symbol} overview时出错: {e}")
                
        logger.info("Overview数据重新生成完成")
        
    def run_cleanup_and_copy(self):
        """执行完整的清理和复制流程"""
        logger.info("=" * 60)
        logger.info("开始执行数据库清理和复制任务")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # 1. 获取需要清理的symbol
            symbols_with_underscore = self.get_symbols_with_underscore()
            if not symbols_with_underscore:
                logger.warning("未找到带_的symbol，退出")
                return
                
            base_symbols = self.get_base_symbols_from_underscore(symbols_with_underscore)
            
            # 2. 删除相关记录
            self.delete_records_by_batch(base_symbols)
            
            # 3. 获取需要复制的symbol
            symbols_to_copy = self.get_temp_test_symbols()
            if not symbols_to_copy:
                logger.warning("未找到需要复制的symbol，跳过复制步骤")
            else:
                # 4. 复制数据
                self.copy_symbols_by_batch(symbols_to_copy)
                
                # 5. 重新生成overview
                self.regenerate_overview(symbols_to_copy)
            
            end_time = time.time()
            logger.success(f"任务完成，总耗时: {end_time - start_time:.2f} 秒")
            
        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            raise

def main():
    """主函数"""
    source_db = "vnpy_stk_us_ib_m_2206_250814"
    target_db = "vnpy_stk_us_ib_m_2206_250814bak"
    
    logger.info(f"源数据库: {source_db}")
    logger.info(f"目标数据库: {target_db}")
    
    cleaner = DatabaseCleanupCopier(source_db, target_db)
    cleaner.run_cleanup_and_copy()

if __name__ == "__main__":
    main()