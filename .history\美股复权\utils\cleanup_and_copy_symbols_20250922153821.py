#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库清理和复制工具
删除vnpy_stk_us_ib_m_2206_250814bak中symbol带_的记录，
然后从vnpy_stk_us_ib_m_2206_250814复制temp_test_symbols中的数据
"""

import os
import sys
import time
from typing import List, Set
from datetime import datetime

from loguru import logger

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from vnpy.trader.setting import SETTINGS
from utils.mysql_database import create_mysql_database

# 配置日志
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level="INFO",
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00"
)

class DatabaseCleanupCopier:
    """数据库清理和复制工具"""
    
    def __init__(self, source_db: str, target_db: str):
        """
        初始化
        Args:
            source_db: 源数据库名称 vnpy_stk_us_ib_m_2206_250814
            target_db: 目标数据库名称 vnpy_stk_us_ib_m_2206_250814bak
        """
        self.source_db = source_db
        self.target_db = target_db
        
        # 创建数据库连接
        source_settings = SETTINGS.copy()
        source_settings['database.database'] = source_db
        
        target_settings = SETTINGS.copy()
        target_settings['database.database'] = target_db
        
        logger.info(f"连接源数据库: {source_db}")
        self.source_database, self.SourceDbBarData, _, self.SourceDbBarOverview, _ = create_mysql_database(
            source_settings, replace_on_conflict=True
        )
        
        logger.info(f"连接目标数据库: {target_db}")
        self.target_database, self.TargetDbBarData, _, self.TargetDbBarOverview, _ = create_mysql_database(
            target_settings, replace_on_conflict=True
        )
        
        logger.info("数据库连接初始化完成")
        
    def get_symbols_with_underscore(self) -> Set[str]:
        """获取目标数据库中symbol带_的所有记录"""
        logger.info("查找目标数据库中symbol带_的记录...")
        
        query = self.TargetDbBarOverview.select(self.TargetDbBarOverview.symbol).where(
            self.TargetDbBarOverview.symbol.contains('_')
        ).distinct()
        
        symbols_with_underscore = set()
        for row in query:
            symbols_with_underscore.add(row.symbol)
            
        logger.info(f"找到 {len(symbols_with_underscore)} 个带_的symbol")
        return symbols_with_underscore
        
    def get_base_symbols_from_underscore(self, symbols_with_underscore: Set[str]) -> Set[str]:
        """从带_的symbol中提取基础symbol（_前面的部分）"""
        base_symbols = set()
        for symbol in symbols_with_underscore:
            base_symbol = symbol.split('_')[0]
            base_symbols.add(base_symbol)
        
        logger.info(f"提取到 {len(base_symbols)} 个基础symbol")
        return base_symbols
        
    def get_temp_test_symbols(self) -> Set[str]:
        """从temp_test_symbols表获取需要复制的symbol列表"""
        try:
            # 尝试直接查询temp_test_symbols表
            sql = "SELECT DISTINCT symbol FROM temp_test_symbols"
            cursor = self.source_database.db.execute_sql(sql)
            symbols = {row[0] for row in cursor.fetchall()}
            logger.info(f"从temp_test_symbols表获取到 {len(symbols)} 个symbol")
            return symbols
        except Exception as e:
            logger.error(f"无法查询temp_test_symbols表: {e}")
            return set()
            
    def delete_records_by_batch(self, base_symbols: Set[str], batch_size: int = 200000):
        """分批删除目标数据库中的相关记录 - 使用SQL直接操作"""
        logger.info(f"开始分批删除记录，批量大小: {batch_size}")
        
        # 删除dbbardata中的记录
        total_deleted_data = 0
        for base_symbol in base_symbols:
            while True:
                # 使用SQL直接删除，避免ORM开销，_需要转义
                sql = f"""
                DELETE FROM {self.target_db}.dbbardata
                WHERE (symbol = %s OR symbol LIKE %s)
                LIMIT {batch_size}
                """
                
                cursor = self.target_database.db.execute_sql(sql, [base_symbol, f"{base_symbol}\\_%"])
                deleted_count = cursor.rowcount
                total_deleted_data += deleted_count
                
                logger.info(f"删除symbol {base_symbol} 相关的 {deleted_count} 条dbbardata记录")
                
                if deleted_count < batch_size:
                    break  # 该symbol的所有记录已删除完毕
                    
                time.sleep(0.1)  # 短暂休息避免数据库压力过大
                
        # 删除dbbaroverview中的记录
        total_deleted_overview = 0
        for base_symbol in base_symbols:
            sql = f"""
            DELETE FROM {self.target_db}.dbbaroverview
            WHERE symbol = %s OR symbol LIKE %s
            """
            
            cursor = self.target_database.db.execute_sql(sql, [base_symbol, f"{base_symbol}\\_%"])
            deleted_count = cursor.rowcount
            total_deleted_overview += deleted_count
            logger.info(f"删除symbol {base_symbol} 相关的 {deleted_count} 条dbbaroverview记录")
            
        logger.info(f"删除完成 - 共删除 {total_deleted_data} 条dbbardata记录，{total_deleted_overview} 条dbbaroverview记录")

    def _copy_single_symbol(self, symbol: str):
        """复制单个symbol的所有相关数据 - 使用SQL直接操作"""
        try:
            # 使用SQL直接复制数据，避免ORM开销，_需要转义
            sql = f"""
            INSERT INTO {self.target_db}.dbbardata
            (symbol, exchange, datetime, `interval`, volume, turnover, open_interest,
             open_price, high_price, low_price, close_price)
            SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest,
                   open_price, high_price, low_price, close_price
            FROM {self.source_db}.dbbardata
            WHERE symbol = %s OR symbol LIKE %s
            ON DUPLICATE KEY UPDATE
                exchange = VALUES(exchange),
                datetime = VALUES(datetime),
                `interval` = VALUES(`interval`),
                volume = VALUES(volume),
                turnover = VALUES(turnover),
                open_interest = VALUES(open_interest),
                open_price = VALUES(open_price),
                high_price = VALUES(high_price),
                low_price = VALUES(low_price),
                close_price = VALUES(close_price)
            """
            
            cursor = self.target_database.db.execute_sql(sql, [symbol, f"{symbol}\\_%"])
            record_count = cursor.rowcount
                
            logger.info(f"复制symbol {symbol}: {record_count} 条记录")
            
        except Exception as e:
            logger.error(f"复制symbol {symbol} 时出错: {e}")
        
    def copy_symbols_by_batch(self, symbols_to_copy: Set[str], symbols_per_batch: int = 10):
        """分批复制symbol数据"""
        logger.info(f"开始分批复制数据，每批处理 {symbols_per_batch} 个symbol")
        
        symbols_list = list(symbols_to_copy)
        total_symbols = len(symbols_list)
        
        for i in range(0, total_symbols, symbols_per_batch):
            batch_symbols = symbols_list[i:i + symbols_per_batch]
            logger.info(f"处理批次 {i//symbols_per_batch + 1}，symbol数量: {len(batch_symbols)}")
            
            for symbol in batch_symbols:
                self._copy_single_symbol(symbol)
                time.sleep(0.05)  # 短暂休息
                
        logger.info("所有symbol数据复制完成")
        
    def _copy_single_symbol(self, symbol: str):
        """复制单个symbol的所有相关数据 - 使用SQL直接操作"""
        try:
            # 使用SQL直接复制数据，避免ORM开销
            sql = f"""
            INSERT INTO {self.target_db}.dbbardata
            (symbol, exchange, datetime, `interval`, volume, turnover, open_interest,
             open_price, high_price, low_price, close_price)
            SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest,
                   open_price, high_price, low_price, close_price
            FROM {self.source_db}.dbbardata
            WHERE symbol = %s OR symbol LIKE %s
            ON DUPLICATE KEY UPDATE
                exchange = VALUES(exchange),
                datetime = VALUES(datetime),
                `interval` = VALUES(`interval`),
                volume = VALUES(volume),
                turnover = VALUES(turnover),
                open_interest = VALUES(open_interest),
                open_price = VALUES(open_price),
                high_price = VALUES(high_price),
                low_price = VALUES(low_price),
                close_price = VALUES(close_price)
            """
            
            cursor = self.target_database.db.execute_sql(sql, [symbol, f"{symbol}_%"])
            record_count = cursor.rowcount
                
            logger.info(f"复制symbol {symbol}: {record_count} 条记录")
            
        except Exception as e:
            logger.error(f"复制symbol {symbol} 时出错: {e}")
            
    def _insert_batch_data(self, batch_data: List[dict]):
        """批量插入数据到目标数据库 - 已弃用，使用SQL直接操作"""
        # 这个方法已经不再使用，保留为兼容性
        pass
            
    def regenerate_overview(self, symbols: Set[str]):
        """重新生成overview数据 - 使用SQL语句直接操作"""
        logger.info("重新生成overview数据...")
        
        try:
            # 构建symbol条件列表用于SQL查询
            symbols_list = list(symbols)
            
            # 分批处理符号列表，每批处理50个symbol
            batch_size = 50
            for i in range(0, len(symbols_list), batch_size):
                batch_symbols = symbols_list[i:i + batch_size]
                
                # 构建WHERE条件，注意_需要转义
                conditions = []
                for symbol in batch_symbols:
                    # 精确匹配symbol或以symbol_开头，_需要转义
                    conditions.append(f"(symbol = %s OR symbol LIKE %s)")
                
                where_clause = " OR ".join(conditions)
                
                # 准备参数，_需要转义
                params = []
                for symbol in batch_symbols:
                    params.append(symbol)  # 精确匹配
                    params.append(f"{symbol}\\_%")  # LIKE匹配，_转义
                
                # 使用SQL直接生成overview数据
                sql = f"""
                INSERT INTO {self.target_db}.dbbaroverview (symbol, exchange, `interval`, `count`, `start`, `end`)
                SELECT
                    symbol,
                    exchange,
                    `interval`,
                    COUNT(id) AS `count`,
                    MIN(datetime) AS `start`,
                    MAX(datetime) AS `end`
                FROM
                    {self.target_db}.dbbardata
                WHERE {where_clause}
                GROUP BY
                    symbol, exchange, `interval`
                ON DUPLICATE KEY UPDATE
                    `count` = VALUES(`count`),
                    `start` = VALUES(`start`),
                    `end` = VALUES(`end`)
                """
                
                self.target_database.db.execute_sql(sql, params)
                logger.info(f"完成批次 {i//batch_size + 1}，处理了 {len(batch_symbols)} 个symbol的overview重建")
                
        except Exception as e:
            logger.error(f"重新生成overview时出错: {e}")
            
        logger.info("Overview数据重新生成完成")
        
    def run_cleanup_and_copy(self):
        """执行完整的清理和复制流程"""
        logger.info("=" * 60)
        logger.info("开始执行数据库清理和复制任务")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # 1. 获取需要清理的symbol
            symbols_with_underscore = self.get_symbols_with_underscore()
            if not symbols_with_underscore:
                logger.warning("未找到带_的symbol，退出")
                return
                
            base_symbols = self.get_base_symbols_from_underscore(symbols_with_underscore)
            
            # 2. 删除相关记录
            self.delete_records_by_batch(base_symbols)
            
            # 3. 获取需要复制的symbol
            symbols_to_copy = self.get_temp_test_symbols()
            if not symbols_to_copy:
                logger.warning("未找到需要复制的symbol，跳过复制步骤")
            else:
                # 4. 复制数据
                self.copy_symbols_by_batch(symbols_to_copy)
                
                # 5. 重新生成overview
                self.regenerate_overview(symbols_to_copy)
            
            end_time = time.time()
            logger.success(f"任务完成，总耗时: {end_time - start_time:.2f} 秒")
            
        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            raise

def main():
    """主函数"""
    source_db = "vnpy_stk_us_ib_m_2206_250814"
    target_db = "vnpy_stk_us_ib_m_2206_250814bak"
    
    logger.info(f"源数据库: {source_db}")
    logger.info(f"目标数据库: {target_db}")
    
    cleaner = DatabaseCleanupCopier(source_db, target_db)
    cleaner.run_cleanup_and_copy()

if __name__ == "__main__":
    main()