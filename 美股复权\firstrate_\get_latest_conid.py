from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, get_file_path
SETTINGS.update(load_json('vt_setting_remote.json'))
import sys, os
# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
import pandas as pd
import re
import glob
import numpy as np
import typer
from loguru import logger

# 定义NA值集合
from utils.mixin import NA_VALUES

# 导入数据库相关模块
from utils.database_manager import FirstrateStock, IbProduct

"""
IB和FirstRate数据匹配模块，主要功能：
1. IB标的筛选
2. FirstRate数据匹配
3. 获取matched_df里面的conid和firstrate_symbol用于交易时间填充和前复权
"""

logger.add("logs/get_latest_conid.log", rotation="10 MB", level="TRACE", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")

class ConidRetriever:
    """数据处理器，负责IB和FirstRate数据的匹配处理，获取matched_df里面的conid和firstrate_symbol用于交易时间填充和前复权"""
    
    def __init__(self, data_dir="S:\\firstrate\\stock", firstrate_data_path="full_1min"):
        """初始化
        
        Args:
            data_dir: FirstRate数据所在的基础目录
            firstrate_data_path: FirstRate数据文件夹名称，默认为"full_1min"，可选"month_1min"等
        """
        # 文件路径定义
        self.base_data_dir = data_dir
        self.firstrate_data_path = firstrate_data_path
        self.firstrate_dir = os.path.join(self.base_data_dir, self.firstrate_data_path)
        
        # IB数据文件
        self.latest_records_file = 'latest_records.csv'
        self.filtered_symbols_file = 'filtered_symbols.csv'
        self.filtered_out_symbols_file = 'filtered_out_symbols.csv'
        self.non_pure_uppercase_symbols_file = 'non_pure_uppercase_symbols.csv'
        
        # FirstRate匹配文件
        self.matched_symbols_file = f'matched_symbols_{firstrate_data_path}.csv'
        self.matched_delisted_symbols_file = f'matched_delisted_symbols_{firstrate_data_path}.csv'
        self.unmatched_symbols_file = f'unmatched_symbols_{firstrate_data_path}.csv'
        self.firstrate_only_symbols_file = f'firstrate_only_symbols_{firstrate_data_path}.csv'
        
        # 数据缓存
        self.all_ib_df = None
        self.filtered_df = None
        self.filtered_out_df = None
        self.matched_df = None
        self.matched_delisted_df = None
        self.unmatched_df = None
        self.firstrate_only_df = None
        self.firstrate_symbols = []
        
        # 纯大写字母符号集合
        self.pure_uppercase_symbols = set()

        # 确保目录存在
        self.ensure_dir(self.firstrate_dir)
    
    def ensure_dir(self, directory):
        """确保目录存在"""
        os.makedirs(directory, exist_ok=True)
        
    def save_to_csv(self, df, filename):
        """保存DataFrame到CSV文件"""
        filepath = get_file_path(filename)
        df.to_csv(filepath, index=False)
        return filepath
        
    def load_from_csv(self, filename):
        """从CSV文件加载DataFrame"""
        filepath = get_file_path(filename)
        if os.path.exists(filepath):
            df = pd.read_csv(
                filepath,
                na_values=NA_VALUES,
                keep_default_na=False
            )
            df = df.replace({np.nan: None})
            return df
        return None
        
    def not_contains_non_uppercase_az(self, symbol):
        """检查symbol是否为纯大写字母"""
        if not isinstance(symbol, str):
            return False
        return all(c.isalpha() and c.isupper() for c in symbol)

    def _update_pure_uppercase_symbols(self):
        """更新纯大写字母symbol集合"""
        if self.all_ib_df is not None:
            self.pure_uppercase_symbols = set(
                self.all_ib_df[self.all_ib_df['symbol'].apply(self.not_contains_non_uppercase_az)]['symbol']
            )

    def get_ib_data(self, force_refresh=False):
        """获取IB数据
        
        Args:
            force_refresh: 是否强制刷新数据
            
        Returns:
            tuple: (筛选后的符号DataFrame, 所有符号DataFrame)
        """            
        # 检查是否有现有文件
        if not force_refresh:
            self.filtered_df = self.load_from_csv(self.filtered_symbols_file)
            self.all_ib_df = self.load_from_csv(self.latest_records_file)
            
            # 如果文件存在，返回加载的数据
            if self.filtered_df is not None and self.all_ib_df is not None:
                logger.info("从文件加载IB数据成功")
                return self.filtered_df, self.all_ib_df
                
        # 如果没有文件或强制刷新，从数据库获取数据
        logger.info("从IB数据库获取数据")
        self.filtered_df, self.all_ib_df = self.get_filtered_symbols_from_ib()
        return self.filtered_df, self.all_ib_df
    
    def get_filtered_symbols_from_ib(self):
        """从IB数据库获取并筛选股票标的"""
        # 使用 ORM 查询所有记录
        query = IbProduct.select(IbProduct.conid, IbProduct.symbol, IbProduct.description, IbProduct.created_time, IbProduct.group_id)
        rows = [(row.conid, row.symbol, row.description, row.created_time, row.group_id) for row in query]
        df = pd.DataFrame(rows, columns=['conid', 'symbol', 'description', 'created_time', 'group_id'])

        logger.info(f"从IB数据库获取的原始记录数量: {len(df)}")

        # 对DataFrame进行处理：对于相同的symbol，选择created_time最新的记录
        # 先按symbol和created_time排序（created_time降序，确保最新的在前）
        df_sorted = df.sort_values(by=['symbol', 'created_time'], ascending=[True, False])
        # 然后去除重复的symbol，保留第一个（即created_time最新的）
        df_filtered_latest_symbol = df_sorted.drop_duplicates(subset=['symbol'], keep='first')
        
        logger.info(f"按symbol筛选后，保留的最新symbol记录数量: {len(df_filtered_latest_symbol)}")

        # 将筛选后的最新数据赋值给 self.all_ib_df
        self.all_ib_df = df_filtered_latest_symbol
        self.save_to_csv(self.all_ib_df, self.latest_records_file)

        # 更新纯大写字母符号集合
        self._update_pure_uppercase_symbols()

        # 应用筛选规则
        self.filtered_df, self.filtered_out_df = self._apply_filters()

        # 保存筛选后的记录
        if not self.filtered_df.empty:
            self.save_to_csv(self.filtered_df, self.filtered_symbols_file)
            logger.info(f"筛选后的symbol数量: {len(self.filtered_df)}")

            # 保存非纯大写字母的筛选后记录
            non_pure_uppercase_df = self.filtered_df[~self.filtered_df['symbol'].apply(self.not_contains_non_uppercase_az)]
            if not non_pure_uppercase_df.empty:
                self.save_to_csv(non_pure_uppercase_df, self.non_pure_uppercase_symbols_file)
                logger.info(f"筛选后非纯大写字母的symbol数量: {len(non_pure_uppercase_df)}")

        # 保存被筛选掉的记录
        if not self.filtered_out_df.empty:
            self.save_to_csv(self.filtered_out_df, self.filtered_out_symbols_file)
            logger.info(f"被筛选掉的symbol数量: {len(self.filtered_out_df)}")
        
        return self.filtered_df, self.all_ib_df
        
    def _apply_filters(self):
        """应用所有筛选规则"""
        # 整合所有筛选规则到一个函数
        def filter_symbol(symbol):
            if not isinstance(symbol, str):
                return True
            
            # 1. 检查是否是纯大写字母symbol的变体
            # for pure_symbol in self.pure_uppercase_symbols:
            #     if symbol.startswith(f"{pure_symbol}.") or symbol.startswith(f"{pure_symbol} "):
            #         return False

            # 2. 检查是否以".OLD"或".OLD1"结尾
            if re.search(r'\.OLD[1]{0,1}$', symbol):
                return False
            
            # 3. 检查是否以特定后缀结尾
            # suffixes = [' RT', ' PR', ' WI', ' WS', ' RTWI', '.RSTD']
            # if any(symbol.endswith(suffix) for suffix in suffixes):
            #     return False

            # 4. 检查是否是测试标的
            if "TESTTE33" == symbol:
                return False
            
            return True

        # 应用所有筛选规则
        filtered_df = self.all_ib_df[self.all_ib_df['symbol'].apply(filter_symbol)]
        filtered_out_df = self.all_ib_df[~self.all_ib_df['symbol'].apply(filter_symbol)]
        
        return filtered_df, filtered_out_df
    
    def get_firstrate_symbols(self, dir_path=None):
        """获取FirstRate数据目录中的所有符号"""
        if dir_path is None:
            dir_path = self.firstrate_dir
            
        firstrate_files = []
        try:
            firstrate_files = [os.path.basename(f) for f in glob.glob(os.path.join(dir_path, "*.txt"))]
        except Exception as e:
            logger.error(f"获取FirstRate文件列表时出错: {e}")
        
        # 提取FirstRate的标的符号
        firstrate_symbols = []
        for file in firstrate_files:
            # 确定文件后缀
            file_suffix = f"_{self.firstrate_data_path}_UNADJUSTED.txt"
            
            # 去掉对应后缀
            if file.endswith(file_suffix):
                symbol = file.replace(file_suffix, "")
                firstrate_symbols.append(symbol)
            
        self.firstrate_symbols = firstrate_symbols
        return firstrate_symbols
        
    def get_firstrate_symbols_from_db(self):
        """从数据库中获取FirstRate符号"""
        # 查询FirstrateStock表中的所有ticker
        query = FirstrateStock.select(FirstrateStock.ticker)
        symbols = [record.ticker for record in query]
        
        logger.info(f"从数据库中找到 {len(symbols)} 个FirstRate符号")
        self.firstrate_symbols = symbols
        return symbols

    def get_possible_firstrate_symbols(self, ib_symbol):
        """获取可能的FirstRate符号列表"""
        if ib_symbol is None:
            return []
                
        symbols = [ib_symbol]  # 原始符号
        
        # 处理可能的" [A-Z]"和" PR[A-Z]"后缀
        match = re.search(r'(.*) (?:PR)?([A-Z])$', str(ib_symbol))
        if match:
            base_symbol = match.group(1)
            suffix = match.group(2)
            firstrate_symbol = f"{base_symbol}.{suffix}"
            symbols.append(firstrate_symbol)
        
        return symbols
        
    def match_symbols(self):
        """匹配IB标的与FirstRate数据"""
        # 确保已获取IB数据
        if self.filtered_df is None or self.all_ib_df is None:
            self.get_ib_data()
            
        # 获取FirstRate符号列表
        if not self.firstrate_symbols:
            self.get_firstrate_symbols()
        
        # 匹配结果
        matched_records = []
        unmatched_records = []
        
        # 用于处理相同group_id的记录
        group_id_records = {}  # group_id -> [record1, record2, ...]
        
        # 遍历筛选后的IB标的
        for _, row in self.filtered_df.iterrows():
            ib_symbol = row.get('symbol')
            group_id = row.get('group_id')
            created_time = row.get('created_time')
            
            # 跳过空值
            if ib_symbol is None:
                continue
                
            possible_symbols = self.get_possible_firstrate_symbols(ib_symbol)
            
            matched = False
            for symbol in possible_symbols:
                if symbol in self.firstrate_symbols:
                    record = {
                        'conid': row.get('conid'),
                        'ib_symbol': ib_symbol,
                        'firstrate_symbol': symbol,
                        'description': row.get('description'),
                        'group_id': group_id,
                        'created_time': created_time
                    }
                    
                    # 将记录添加到对应的group_id组
                    if group_id not in group_id_records:
                        group_id_records[group_id] = []
                    group_id_records[group_id].append(record)
                    
                    matched = True
                    break
            
            if not matched:
                unmatched_records.append({
                    'conid': row.get('conid'),
                    'ib_symbol': ib_symbol,
                    'description': row.get('description'),
                    'created_time': created_time
                })
        
        # 处理相同group_id的记录，只保留created_time最新的
        matched_records = []
        delisted_records = []
        group_id_duplicates = set()
        
        for group_id, records in group_id_records.items():
            if len(records) > 1:
                # 记录重复的group_id
                group_id_duplicates.add(group_id)
                
                # 按created_time降序排序，保留最新的记录
                sorted_records = sorted(records, key=lambda x: x['created_time'], reverse=True)
                matched_records.append(sorted_records[0])  # 添加最新的记录到matched_records
                delisted_records.extend(sorted_records[1:])  # 添加其他记录到delisted_records
                
                logger.info(f"处理重复group_id {group_id}: 保留最新记录 {sorted_records[0]['ib_symbol']}，移除 {len(sorted_records)-1} 条旧记录")
            else:
                # 只有一条记录，直接添加
                matched_records.append(records[0])
        
        # 查找FirstRate中有但IB中没有的符号
        firstrate_only_records = []
        
        # 获取所有IB符号
        all_ib_symbols = set()
        # 考虑特殊处理后的符号 - 将IB的" [A-Z]"结尾的也视为".[A-Z]"
        for symbol in self.all_ib_df['symbol'].unique():
            possible_symbols = self.get_possible_firstrate_symbols(symbol)
            all_ib_symbols.update(possible_symbols)
        
        # 查找FirstRate中有但IB中没有的符号
        for symbol in self.firstrate_symbols:
            if symbol not in all_ib_symbols:
                firstrate_only_records.append({
                    'firstrate_symbol': symbol
                })
        
        # 创建DataFrame
        self.matched_df = pd.DataFrame(matched_records)
        self.matched_delisted_df = pd.DataFrame(delisted_records)
        self.unmatched_df = pd.DataFrame(unmatched_records)
        self.firstrate_only_df = pd.DataFrame(firstrate_only_records)
        
        # 检查group_id重复并记录日志
        if group_id_duplicates:
            logger.info(f"处理了 {len(group_id_duplicates)} 个重复的group_id，已将旧记录移至delisted文件")
        
        # 保存结果
        self._save_results()
        
        return self.matched_df, self.unmatched_df, self.firstrate_only_df
        
    def _save_results(self):
        """保存匹配结果到CSV文件"""
        if len(self.matched_df) > 0:
            self.save_to_csv(self.matched_df, self.matched_symbols_file)
            logger.info(f"匹配到FirstRate数据的符号数量: {len(self.matched_df)}")
        else:
            empty_df = pd.DataFrame(columns=['conid', 'ib_symbol', 'firstrate_symbol', 'description', 'group_id', 'created_time'])
            self.save_to_csv(empty_df, self.matched_symbols_file)
            logger.info("没有匹配到任何FirstRate数据")
        
        # 保存被替换的旧记录（相同group_id但created_time较旧的记录）
        if len(self.matched_delisted_df) > 0:
            self.save_to_csv(self.matched_delisted_df, self.matched_delisted_symbols_file)
            logger.info(f"被替换的旧记录数量: {len(self.matched_delisted_df)}")
        else:
            empty_df = pd.DataFrame(columns=['conid', 'ib_symbol', 'firstrate_symbol', 'description', 'group_id', 'created_time'])
            self.save_to_csv(empty_df, self.matched_delisted_symbols_file)
            logger.info("没有被替换的旧记录")
        
        if len(self.unmatched_df) > 0:
            self.save_to_csv(self.unmatched_df, self.unmatched_symbols_file)
            logger.info(f"未匹配到FirstRate数据的符号数量: {len(self.unmatched_df)}")
        else:
            empty_df = pd.DataFrame(columns=['conid', 'ib_symbol', 'description', 'created_time'])
            self.save_to_csv(empty_df, self.unmatched_symbols_file)
            logger.info("没有未匹配的符号")
        
        if len(self.firstrate_only_df) > 0:
            self.save_to_csv(self.firstrate_only_df, self.firstrate_only_symbols_file)
            logger.info(f"FirstRate中有但IB中没有的符号数量: {len(self.firstrate_only_df)}")
        else:
            empty_df = pd.DataFrame(columns=['firstrate_symbol'])
            self.save_to_csv(empty_df, self.firstrate_only_symbols_file)
            logger.info("没有FirstRate特有的符号")
    
    def process(self, force_refresh=False):
        """执行匹配处理流程
        
        Args:
            force_refresh: 是否强制刷新IB数据
            
        Returns:
            dict: 包含匹配结果的字典
        """
        # 1. 获取并筛选IB数据
        self.get_ib_data(force_refresh)
        
        # 2. 如果还没有获取FirstRate符号，则从本地文件获取
        if not self.firstrate_symbols:
            self.get_firstrate_symbols()
            
        # 3. 匹配FirstRate数据
        self.match_symbols()
        
        return {
            'matched_df': self.matched_df,
            'unmatched_df': self.unmatched_df,
            'firstrate_only_df': self.firstrate_only_df,
            'firstrate_data_path': self.firstrate_data_path
        }
    
app = typer.Typer()

@app.command()
def process(
    data_path: str = typer.Option(
        "full_1min",
        "--data-path",
        "-p",
        help="数据路径，可选 full_1min 或 month_1min"
    ),
    data_dir: str = typer.Option(
        "S:\\firstrate\\stock",
        "--data-dir",
        "-d",
        help="FirstRate数据目录"
    ),
    force_refresh: bool = typer.Option(
        True,
        "--force-refresh",
        "-f",
        help="强制刷新IB数据"
    ),
    use_database: bool = typer.Option(
        False,
        "--use-database",
        "-db",
        help="使用数据库获取FirstRate符号，而不是本地文件"
    )
):
    """获取最新的conid和firstrate_symbol匹配关系"""
    # 检查当前是否为交易日
    from datetime import datetime
    from vnpy.trader.utility import ZoneInfo
    import pandas_market_calendars as mcal
    
    ET_TZ = ZoneInfo("America/New_York")  # 美东时间
    nyse = mcal.get_calendar('NYSE')
    now = datetime.now(ET_TZ)
    today = now.date()
    schedule = nyse.schedule(start_date=today, end_date=today)
    if schedule.empty:
        logger.warning(f"当前日期 {today} 不是交易日，程序退出")
        return
        
    # 创建处理器
    retriever = ConidRetriever(data_dir=data_dir, firstrate_data_path=data_path)
    
    # 执行处理流程
    if use_database:
        # 使用数据库模式
        retriever.get_firstrate_symbols_from_db()
    
    results = retriever.process(force_refresh=force_refresh)
    
    logger.info("处理完成!")
    logger.info(f"数据路径: {results['firstrate_data_path']}")
    logger.info(f"匹配的符号: {len(results['matched_df'])}，未匹配的符号: {len(results['unmatched_df'])}，FirstRate特有的符号: {len(results['firstrate_only_df'])}")

if __name__ == "__main__":
    app()